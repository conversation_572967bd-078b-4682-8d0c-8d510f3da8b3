import 'package:flutter/material.dart';
import '../../widgets/common/custom_card.dart';
import '../../../core/constants/app_constants.dart';

class ExpenseDetailScreen extends StatelessWidget {
  final String expenseId;
  
  const ExpenseDetailScreen({super.key, required this.expenseId});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Ausgabe Details')),
      body: Center(
        child: CustomCard(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Icon(Icons.receipt, size: 64),
              const SizedBox(height: AppConstants.mediumSpacing),
              const Text('Ausgabe Details', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
              Text('Expense ID: $expenseId'),
              const Text('Wird bald implementiert'),
            ],
          ),
        ),
      ),
    );
  }
}
