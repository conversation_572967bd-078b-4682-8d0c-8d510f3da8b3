import 'package:flutter/material.dart';
import '../../../core/constants/app_constants.dart';

enum CardType { elevated, outlined, filled }

class CustomCard extends StatelessWidget {
  final Widget child;
  final EdgeInsets? padding;
  final EdgeInsets? margin;
  final VoidCallback? onTap;
  final CardType type;
  final Color? backgroundColor;
  final Color? borderColor;
  final double? elevation;
  final double? borderRadius;
  final bool showShadow;

  const CustomCard({
    super.key,
    required this.child,
    this.padding,
    this.margin,
    this.onTap,
    this.type = CardType.elevated,
    this.backgroundColor,
    this.borderColor,
    this.elevation,
    this.borderRadius,
    this.showShadow = true,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    
    Widget card = _buildCard(context, theme, colorScheme);
    
    if (onTap != null) {
      card = InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(borderRadius ?? AppConstants.mediumRadius),
        child: card,
      );
    }
    
    return Container(
      margin: margin,
      child: card,
    );
  }

  Widget _buildCard(BuildContext context, ThemeData theme, ColorScheme colorScheme) {
    switch (type) {
      case CardType.elevated:
        return _buildElevatedCard(colorScheme);
      case CardType.outlined:
        return _buildOutlinedCard(colorScheme);
      case CardType.filled:
        return _buildFilledCard(colorScheme);
    }
  }

  Widget _buildElevatedCard(ColorScheme colorScheme) {
    return Card(
      elevation: showShadow ? (elevation ?? 2) : 0,
      color: backgroundColor ?? colorScheme.surface,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(borderRadius ?? AppConstants.mediumRadius),
      ),
      child: Padding(
        padding: padding ?? const EdgeInsets.all(AppConstants.mediumSpacing),
        child: child,
      ),
    );
  }

  Widget _buildOutlinedCard(ColorScheme colorScheme) {
    return Container(
      decoration: BoxDecoration(
        color: backgroundColor ?? colorScheme.surface,
        borderRadius: BorderRadius.circular(borderRadius ?? AppConstants.mediumRadius),
        border: Border.all(
          color: borderColor ?? colorScheme.outline,
          width: 1,
        ),
        boxShadow: showShadow ? [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ] : null,
      ),
      child: Padding(
        padding: padding ?? const EdgeInsets.all(AppConstants.mediumSpacing),
        child: child,
      ),
    );
  }

  Widget _buildFilledCard(ColorScheme colorScheme) {
    return Container(
      decoration: BoxDecoration(
        color: backgroundColor ?? colorScheme.surfaceVariant,
        borderRadius: BorderRadius.circular(borderRadius ?? AppConstants.mediumRadius),
        boxShadow: showShadow ? [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ] : null,
      ),
      child: Padding(
        padding: padding ?? const EdgeInsets.all(AppConstants.mediumSpacing),
        child: child,
      ),
    );
  }
}

// Specialized card widgets
class InfoCard extends StatelessWidget {
  final String title;
  final String? subtitle;
  final Widget? trailing;
  final IconData? icon;
  final Color? iconColor;
  final VoidCallback? onTap;

  const InfoCard({
    super.key,
    required this.title,
    this.subtitle,
    this.trailing,
    this.icon,
    this.iconColor,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return CustomCard(
      onTap: onTap,
      child: Row(
        children: [
          if (icon != null) ...[
            Container(
              padding: const EdgeInsets.all(AppConstants.smallSpacing),
              decoration: BoxDecoration(
                color: (iconColor ?? colorScheme.primary).withOpacity(0.1),
                borderRadius: BorderRadius.circular(AppConstants.smallRadius),
              ),
              child: Icon(
                icon,
                color: iconColor ?? colorScheme.primary,
                size: AppConstants.mediumIcon,
              ),
            ),
            const SizedBox(width: AppConstants.mediumSpacing),
          ],
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                if (subtitle != null) ...[
                  const SizedBox(height: 4),
                  Text(
                    subtitle!,
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: colorScheme.onSurfaceVariant,
                    ),
                  ),
                ],
              ],
            ),
          ),
          if (trailing != null) ...[
            const SizedBox(width: AppConstants.mediumSpacing),
            trailing!,
          ],
        ],
      ),
    );
  }
}

class StatCard extends StatelessWidget {
  final String title;
  final String value;
  final String? subtitle;
  final IconData? icon;
  final Color? color;
  final VoidCallback? onTap;

  const StatCard({
    super.key,
    required this.title,
    required this.value,
    this.subtitle,
    this.icon,
    this.color,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final cardColor = color ?? colorScheme.primary;

    return CustomCard(
      onTap: onTap,
      backgroundColor: cardColor.withOpacity(0.1),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                title,
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: colorScheme.onSurfaceVariant,
                ),
              ),
              if (icon != null)
                Icon(
                  icon,
                  color: cardColor,
                  size: AppConstants.mediumIcon,
                ),
            ],
          ),
          const SizedBox(height: AppConstants.smallSpacing),
          Text(
            value,
            style: theme.textTheme.headlineMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: cardColor,
            ),
          ),
          if (subtitle != null) ...[
            const SizedBox(height: 4),
            Text(
              subtitle!,
              style: theme.textTheme.bodySmall?.copyWith(
                color: colorScheme.onSurfaceVariant,
              ),
            ),
          ],
        ],
      ),
    );
  }
}
