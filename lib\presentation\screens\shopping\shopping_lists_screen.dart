import 'package:flutter/material.dart';
import '../../widgets/common/custom_card.dart';
import '../../../core/constants/app_constants.dart';

class ShoppingListsScreen extends StatelessWidget {
  const ShoppingListsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Einkaufslisten'),
      ),
      body: const Center(
        child: CustomCard(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(Icons.shopping_cart, size: 64),
              SizedBox(height: AppConstants.mediumSpacing),
              Text('Einkaufslisten Feature', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
              Text('Wird bald implementiert'),
            ],
          ),
        ),
      ),
    );
  }
}

class ShoppingListDetailScreen extends StatelessWidget {
  final String listId;
  
  const ShoppingListDetailScreen({super.key, required this.listId});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Einkaufsliste Details')),
      body: Center(child: Text('List ID: $listId')),
    );
  }
}

class CreateShoppingListScreen extends StatelessWidget {
  const CreateShoppingListScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Neue Einkaufsliste')),
      body: const Center(child: Text('Einkaufsliste erstellen')),
    );
  }
}
