import 'package:flutter/material.dart';
import '../../widgets/common/custom_card.dart';
import '../../../core/constants/app_constants.dart';

class RecipeDetailScreen extends StatelessWidget {
  final String recipeId;
  
  const RecipeDetailScreen({super.key, required this.recipeId});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Rezept Details')),
      body: Center(
        child: CustomCard(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Icon(Icons.restaurant, size: 64),
              const SizedBox(height: AppConstants.mediumSpacing),
              const Text('Rezept Details', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
              Text('Recipe ID: $recipeId'),
              const Text('Wird bald implementiert'),
            ],
          ),
        ),
      ),
    );
  }
}
