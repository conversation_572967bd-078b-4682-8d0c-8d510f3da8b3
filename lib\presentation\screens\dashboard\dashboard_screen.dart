import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/auth_provider.dart';
import '../../providers/family_provider.dart';
import '../../providers/task_provider.dart';
import '../../widgets/common/custom_card.dart';
import '../../widgets/common/avatar_widget.dart';
import '../../../core/constants/app_constants.dart';
import '../../../core/theme/app_colors.dart';
import '../../../core/routing/app_router.dart';

class DashboardScreen extends StatefulWidget {
  const DashboardScreen({super.key});

  @override
  State<DashboardScreen> createState() => _DashboardScreenState();
}

class _DashboardScreenState extends State<DashboardScreen> {
  @override
  void initState() {
    super.initState();
    _loadData();
  }

  void _loadData() {
    final authProvider = context.read<AuthProvider>();
    final familyProvider = context.read<FamilyProvider>();
    final taskProvider = context.read<TaskProvider>();

    if (authProvider.currentUser != null) {
      familyProvider.loadFamily(authProvider.currentUser!.familyId);
      taskProvider.loadTasks(authProvider.currentUser!.familyId);
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final size = MediaQuery.of(context).size;

    return Scaffold(
      body: SafeArea(
        child: RefreshIndicator(
          onRefresh: () async {
            _loadData();
          },
          child: SingleChildScrollView(
            physics: const AlwaysScrollableScrollPhysics(),
            padding: const EdgeInsets.all(AppConstants.mediumSpacing),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header
                _buildHeader(theme),

                const SizedBox(height: AppConstants.largeSpacing),

                // Quick Stats
                _buildQuickStats(theme),

                const SizedBox(height: AppConstants.largeSpacing),

                // Today's Tasks
                _buildTodaysTasks(theme),

                const SizedBox(height: AppConstants.largeSpacing),

                // Upcoming Events
                _buildUpcomingEvents(theme),

                const SizedBox(height: AppConstants.largeSpacing),

                // Family Activity
                _buildFamilyActivity(theme),

                const SizedBox(height: AppConstants.largeSpacing),

                // Quick Actions
                _buildQuickActions(theme),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader(ThemeData theme) {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, child) {
        final user = authProvider.currentUser;
        if (user == null) return const SizedBox.shrink();

        final greeting = _getGreeting();

        return Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    greeting,
                    style: theme.textTheme.titleMedium?.copyWith(
                      color: theme.colorScheme.onSurfaceVariant,
                    ),
                  ),
                  Text(
                    user.firstName,
                    style: theme.textTheme.headlineMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: theme.colorScheme.onSurface,
                    ),
                  ),
                ],
              ),
            ),
            UserAvatarWidget(
              imageUrl: user.profileImageUrl,
              firstName: user.firstName,
              lastName: user.lastName,
              size: AvatarSize.large,
              onTap: () {
                // TODO: Navigate to profile
              },
            ),
          ],
        );
      },
    );
  }

  Widget _buildQuickStats(ThemeData theme) {
    return Consumer<TaskProvider>(
      builder: (context, taskProvider, child) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Übersicht',
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConstants.mediumSpacing),
            Row(
              children: [
                Expanded(
                  child: StatCard(
                    title: 'Offene Aufgaben',
                    value: taskProvider.pendingTasks.length.toString(),
                    icon: Icons.task_outlined,
                    color: AppColors.primary,
                    onTap: () => context.goToTasks(),
                  ),
                ),
                const SizedBox(width: AppConstants.mediumSpacing),
                Expanded(
                  child: StatCard(
                    title: 'Heute fällig',
                    value: taskProvider.todayTasks.length.toString(),
                    icon: Icons.today_outlined,
                    color: AppColors.warning,
                    onTap: () => context.goToTasks(),
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppConstants.mediumSpacing),
            Row(
              children: [
                Expanded(
                  child: StatCard(
                    title: 'Überfällig',
                    value: taskProvider.overdueTasks.length.toString(),
                    icon: Icons.warning_outlined,
                    color: AppColors.error,
                    onTap: () => context.goToTasks(),
                  ),
                ),
                const SizedBox(width: AppConstants.mediumSpacing),
                Expanded(
                  child: StatCard(
                    title: 'Abgeschlossen',
                    value: taskProvider.completedTasks.length.toString(),
                    icon: Icons.check_circle_outline,
                    color: AppColors.success,
                    onTap: () => context.goToTasks(),
                  ),
                ),
              ],
            ),
          ],
        );
      },
    );
  }

  Widget _buildTodaysTasks(ThemeData theme) {
    return Consumer<TaskProvider>(
      builder: (context, taskProvider, child) {
        final todayTasks = taskProvider.todayTasks.take(3).toList();

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Heute zu erledigen',
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                TextButton(
                  onPressed: () => context.goToTasks(),
                  child: const Text('Alle anzeigen'),
                ),
              ],
            ),
            const SizedBox(height: AppConstants.mediumSpacing),
            if (todayTasks.isEmpty)
              CustomCard(
                child: Column(
                  children: [
                    Icon(
                      Icons.check_circle_outline,
                      size: 48,
                      color: AppColors.success,
                    ),
                    const SizedBox(height: AppConstants.mediumSpacing),
                    Text(
                      'Keine Aufgaben für heute!',
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    Text(
                      'Du hast alle Aufgaben erledigt.',
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: theme.colorScheme.onSurfaceVariant,
                      ),
                    ),
                  ],
                ),
              )
            else
              ...todayTasks.map((task) => Padding(
                    padding: const EdgeInsets.only(
                        bottom: AppConstants.smallSpacing),
                    child: InfoCard(
                      title: task.title,
                      subtitle: task.description,
                      icon: _getTaskIcon(task.priority),
                      iconColor: _getTaskColor(task.priority),
                      trailing: Icon(
                        Icons.arrow_forward_ios,
                        size: 16,
                        color: theme.colorScheme.onSurfaceVariant,
                      ),
                      onTap: () => context.goToTaskDetail(task.id),
                    ),
                  )),
          ],
        );
      },
    );
  }

  Widget _buildUpcomingEvents(ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Anstehende Termine',
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            TextButton(
              onPressed: () => context.goToCalendar(),
              child: const Text('Kalender öffnen'),
            ),
          ],
        ),
        const SizedBox(height: AppConstants.mediumSpacing),
        CustomCard(
          child: Column(
            children: [
              Icon(
                Icons.calendar_today_outlined,
                size: 48,
                color: theme.colorScheme.primary,
              ),
              const SizedBox(height: AppConstants.mediumSpacing),
              Text(
                'Keine Termine heute',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              Text(
                'Dein Kalender ist frei.',
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: theme.colorScheme.onSurfaceVariant,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildFamilyActivity(ThemeData theme) {
    return Consumer<FamilyProvider>(
      builder: (context, familyProvider, child) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Familie',
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                TextButton(
                  onPressed: () => context.goToContacts(),
                  child: const Text('Alle anzeigen'),
                ),
              ],
            ),
            const SizedBox(height: AppConstants.mediumSpacing),
            CustomCard(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.family_restroom,
                        color: theme.colorScheme.primary,
                      ),
                      const SizedBox(width: AppConstants.mediumSpacing),
                      Expanded(
                        child: Text(
                          familyProvider.currentFamily?.name ?? 'Familie',
                          style: theme.textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: AppConstants.mediumSpacing),
                  Text(
                    '${familyProvider.familyMembers.length} Mitglieder',
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: theme.colorScheme.onSurfaceVariant,
                    ),
                  ),
                  const SizedBox(height: AppConstants.smallSpacing),
                  Wrap(
                    spacing: AppConstants.smallSpacing,
                    children:
                        familyProvider.familyMembers.take(4).map((member) {
                      return UserAvatarWidget(
                        imageUrl: member.profileImageUrl,
                        firstName: member.firstName,
                        lastName: member.lastName,
                        size: AvatarSize.small,
                      );
                    }).toList(),
                  ),
                ],
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildQuickActions(ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Schnellzugriff',
          style: theme.textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: AppConstants.mediumSpacing),
        GridView.count(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisCount: 2,
          crossAxisSpacing: AppConstants.mediumSpacing,
          mainAxisSpacing: AppConstants.mediumSpacing,
          childAspectRatio: 1.5,
          children: [
            _buildQuickActionCard(
              theme,
              icon: Icons.add_task,
              title: 'Neue Aufgabe',
              color: AppColors.primary,
              onTap: () => context.goToCreateTask(),
            ),
            _buildQuickActionCard(
              theme,
              icon: Icons.restaurant,
              title: 'Neues Rezept',
              color: AppColors.secondary,
              onTap: () => context.goToRecipes(),
            ),
            _buildQuickActionCard(
              theme,
              icon: Icons.document_scanner,
              title: 'Dokument scannen',
              color: AppColors.warning,
              onTap: () => context.goToDocumentScanner(),
            ),
            _buildQuickActionCard(
              theme,
              icon: Icons.event,
              title: 'Termin erstellen',
              color: AppColors.info,
              onTap: () => context.goToCalendar(),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildQuickActionCard(
    ThemeData theme, {
    required IconData icon,
    required String title,
    required Color color,
    required VoidCallback onTap,
  }) {
    return CustomCard(
      onTap: onTap,
      backgroundColor: color.withOpacity(0.1),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            icon,
            size: AppConstants.largeIcon,
            color: color,
          ),
          const SizedBox(height: AppConstants.smallSpacing),
          Text(
            title,
            style: theme.textTheme.titleSmall?.copyWith(
              fontWeight: FontWeight.w600,
              color: color,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  String _getGreeting() {
    final hour = DateTime.now().hour;
    if (hour < 12) {
      return 'Guten Morgen';
    } else if (hour < 17) {
      return 'Guten Tag';
    } else {
      return 'Guten Abend';
    }
  }

  IconData _getTaskIcon(priority) {
    switch (priority.toString()) {
      case 'TaskPriority.urgent':
        return Icons.priority_high;
      case 'TaskPriority.high':
        return Icons.keyboard_arrow_up;
      case 'TaskPriority.medium':
        return Icons.remove;
      case 'TaskPriority.low':
        return Icons.keyboard_arrow_down;
      default:
        return Icons.task;
    }
  }

  Color _getTaskColor(priority) {
    switch (priority.toString()) {
      case 'TaskPriority.urgent':
        return AppColors.error;
      case 'TaskPriority.high':
        return AppColors.warning;
      case 'TaskPriority.medium':
        return AppColors.primary;
      case 'TaskPriority.low':
        return AppColors.success;
      default:
        return AppColors.grey500;
    }
  }
}
