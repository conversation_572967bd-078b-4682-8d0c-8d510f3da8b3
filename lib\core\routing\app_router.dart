import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import '../../presentation/providers/auth_provider.dart';
import '../../presentation/screens/auth/splash_screen.dart';
import '../../presentation/screens/auth/login_screen.dart';
import '../../presentation/screens/auth/register_screen.dart';
import '../../presentation/screens/dashboard/main_screen.dart';
import '../../presentation/screens/dashboard/dashboard_screen.dart';
import '../../presentation/screens/calendar/calendar_screen.dart';
import '../../presentation/screens/tasks/tasks_screen.dart';
import '../../presentation/screens/tasks/task_detail_screen.dart';
import '../../presentation/screens/tasks/create_task_screen.dart';
import '../../presentation/screens/contacts/contacts_screen.dart';
import '../../presentation/screens/contacts/contact_detail_screen.dart';
import '../../presentation/screens/expenses/expenses_screen.dart';
import '../../presentation/screens/expenses/expense_detail_screen.dart';
import '../../presentation/screens/expenses/create_expense_screen.dart';
import '../../presentation/screens/shopping/shopping_lists_screen.dart';
import '../../presentation/screens/shopping/shopping_list_detail_screen.dart';
import '../../presentation/screens/shopping/create_shopping_list_screen.dart';

class AppRouter {
  static final _rootNavigatorKey = GlobalKey<NavigatorState>();
  static final _shellNavigatorKey = GlobalKey<NavigatorState>();

  static GoRouter createRouter() {
    return GoRouter(
      navigatorKey: _rootNavigatorKey,
      initialLocation: '/splash',
      redirect: (context, state) {
        final authProvider = context.read<AuthProvider>();
        final isAuthenticated = authProvider.isAuthenticated;
        final isLoading = authProvider.status == AuthStatus.loading;
        
        // Show splash screen while checking auth status
        if (authProvider.status == AuthStatus.initial || isLoading) {
          return '/splash';
        }
        
        // Redirect to login if not authenticated and not already on auth pages
        if (!isAuthenticated && 
            !state.location.startsWith('/login') && 
            !state.location.startsWith('/register') &&
            state.location != '/splash') {
          return '/login';
        }
        
        // Redirect to dashboard if authenticated and on auth pages
        if (isAuthenticated && 
            (state.location.startsWith('/login') || 
             state.location.startsWith('/register') ||
             state.location == '/splash')) {
          return '/dashboard';
        }
        
        return null;
      },
      routes: [
        // Splash Screen
        GoRoute(
          path: '/splash',
          name: 'splash',
          builder: (context, state) => const SplashScreen(),
        ),
        
        // Auth Routes
        GoRoute(
          path: '/login',
          name: 'login',
          builder: (context, state) => const LoginScreen(),
        ),
        GoRoute(
          path: '/register',
          name: 'register',
          builder: (context, state) => const RegisterScreen(),
        ),
        
        // Main App Shell with Bottom Navigation
        ShellRoute(
          navigatorKey: _shellNavigatorKey,
          builder: (context, state, child) => MainScreen(child: child),
          routes: [
            // Dashboard
            GoRoute(
              path: '/dashboard',
              name: 'dashboard',
              builder: (context, state) => const DashboardScreen(),
            ),
            
            // Calendar
            GoRoute(
              path: '/calendar',
              name: 'calendar',
              builder: (context, state) => const CalendarScreen(),
            ),
            
            // Tasks
            GoRoute(
              path: '/tasks',
              name: 'tasks',
              builder: (context, state) => const TasksScreen(),
              routes: [
                GoRoute(
                  path: '/create',
                  name: 'create-task',
                  builder: (context, state) => const CreateTaskScreen(),
                ),
                GoRoute(
                  path: '/:taskId',
                  name: 'task-detail',
                  builder: (context, state) {
                    final taskId = state.pathParameters['taskId']!;
                    return TaskDetailScreen(taskId: taskId);
                  },
                ),
              ],
            ),
            
            // Contacts
            GoRoute(
              path: '/contacts',
              name: 'contacts',
              builder: (context, state) => const ContactsScreen(),
              routes: [
                GoRoute(
                  path: '/:userId',
                  name: 'contact-detail',
                  builder: (context, state) {
                    final userId = state.pathParameters['userId']!;
                    return ContactDetailScreen(userId: userId);
                  },
                ),
              ],
            ),
            
            // Expenses
            GoRoute(
              path: '/expenses',
              name: 'expenses',
              builder: (context, state) => const ExpensesScreen(),
              routes: [
                GoRoute(
                  path: '/create',
                  name: 'create-expense',
                  builder: (context, state) => const CreateExpenseScreen(),
                ),
                GoRoute(
                  path: '/:expenseId',
                  name: 'expense-detail',
                  builder: (context, state) {
                    final expenseId = state.pathParameters['expenseId']!;
                    return ExpenseDetailScreen(expenseId: expenseId);
                  },
                ),
              ],
            ),
            
            // Shopping Lists
            GoRoute(
              path: '/shopping',
              name: 'shopping',
              builder: (context, state) => const ShoppingListsScreen(),
              routes: [
                GoRoute(
                  path: '/create',
                  name: 'create-shopping-list',
                  builder: (context, state) => const CreateShoppingListScreen(),
                ),
                GoRoute(
                  path: '/:listId',
                  name: 'shopping-list-detail',
                  builder: (context, state) {
                    final listId = state.pathParameters['listId']!;
                    return ShoppingListDetailScreen(listId: listId);
                  },
                ),
              ],
            ),
          ],
        ),
      ],
    );
  }
}

// Extension for easy navigation
extension AppRouterExtension on BuildContext {
  void goToLogin() => go('/login');
  void goToRegister() => go('/register');
  void goToDashboard() => go('/dashboard');
  void goToCalendar() => go('/calendar');
  void goToTasks() => go('/tasks');
  void goToContacts() => go('/contacts');
  void goToExpenses() => go('/expenses');
  void goToShopping() => go('/shopping');
  
  void goToTaskDetail(String taskId) => go('/tasks/$taskId');
  void goToCreateTask() => go('/tasks/create');
  void goToContactDetail(String userId) => go('/contacts/$userId');
  void goToExpenseDetail(String expenseId) => go('/expenses/$expenseId');
  void goToCreateExpense() => go('/expenses/create');
  void goToShoppingListDetail(String listId) => go('/shopping/$listId');
  void goToCreateShoppingList() => go('/shopping/create');
}
