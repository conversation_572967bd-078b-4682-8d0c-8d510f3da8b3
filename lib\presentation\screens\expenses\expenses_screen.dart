import 'package:flutter/material.dart';
import '../../widgets/common/custom_card.dart';
import '../../../core/constants/app_constants.dart';

class ExpensesScreen extends StatelessWidget {
  const ExpensesScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Ausgaben'),
      ),
      body: const Center(
        child: CustomCard(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(Icons.account_balance_wallet, size: 64),
              SizedBox(height: AppConstants.mediumSpacing),
              Text('Ausgaben Feature',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
              Text('Wird bald implementiert'),
            ],
          ),
        ),
      ),
    );
  }
}
