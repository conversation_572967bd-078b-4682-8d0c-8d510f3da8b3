import 'package:flutter/material.dart';
import '../../widgets/common/custom_card.dart';
import '../../../core/constants/app_constants.dart';

class TaskDetailScreen extends StatelessWidget {
  final String taskId;
  
  const TaskDetailScreen({super.key, required this.taskId});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Aufgabe Details')),
      body: Center(
        child: CustomCard(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Icon(Icons.task, size: 64),
              const SizedBox(height: AppConstants.mediumSpacing),
              const Text('Aufgabe Details', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
              Text('Task ID: $taskId'),
              const Text('Wird bald implementiert'),
            ],
          ),
        ),
      ),
    );
  }
}
