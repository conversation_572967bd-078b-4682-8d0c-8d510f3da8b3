import 'package:flutter/material.dart';
import '../../widgets/common/custom_card.dart';
import '../../../core/constants/app_constants.dart';

class CreateShoppingListScreen extends StatelessWidget {
  const CreateShoppingListScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Neue Einkaufsliste')),
      body: const Center(
        child: CustomCard(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(Icons.add_shopping_cart, size: 64),
              SizedBox(height: AppConstants.mediumSpacing),
              Text('Einkaufsliste erstellen', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
              Text('Wird bald implementiert'),
            ],
          ),
        ),
      ),
    );
  }
}
