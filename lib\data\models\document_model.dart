import 'package:equatable/equatable.dart';

enum DocumentType {
  passport,
  idCard,
  drivingLicense,
  insurance,
  medical,
  school,
  work,
  contract,
  receipt,
  warranty,
  certificate,
  other
}

enum DocumentStatus { active, expired, pending, archived }

class DocumentModel extends Equatable {
  final String id;
  final String title;
  final String? description;
  final DocumentType type;
  final DocumentStatus status;
  final String? documentNumber;
  final DateTime? issueDate;
  final DateTime? expiryDate;
  final String? issuingAuthority;
  final List<String> imageUrls;
  final List<String> tags;
  final String uploadedByUserId;
  final String familyId;
  final List<String> sharedWithUserIds;
  final bool isImportant;
  final String? notes;
  final DateTime createdAt;
  final DateTime updatedAt;

  const DocumentModel({
    required this.id,
    required this.title,
    this.description,
    this.type = DocumentType.other,
    this.status = DocumentStatus.active,
    this.documentNumber,
    this.issueDate,
    this.expiryDate,
    this.issuingAuthority,
    this.imageUrls = const [],
    this.tags = const [],
    required this.uploadedByUserId,
    required this.familyId,
    this.sharedWithUserIds = const [],
    this.isImportant = false,
    this.notes,
    required this.createdAt,
    required this.updatedAt,
  });

  bool get isExpiringSoon {
    if (expiryDate == null) return false;
    final now = DateTime.now();
    final daysUntilExpiry = expiryDate!.difference(now).inDays;
    return daysUntilExpiry <= 30 && daysUntilExpiry >= 0;
  }

  bool get isExpired {
    if (expiryDate == null) return false;
    return DateTime.now().isAfter(expiryDate!);
  }

  String get typeText {
    switch (type) {
      case DocumentType.passport:
        return 'Reisepass';
      case DocumentType.idCard:
        return 'Personalausweis';
      case DocumentType.drivingLicense:
        return 'Führerschein';
      case DocumentType.insurance:
        return 'Versicherung';
      case DocumentType.medical:
        return 'Medizinisch';
      case DocumentType.school:
        return 'Schule';
      case DocumentType.work:
        return 'Arbeit';
      case DocumentType.contract:
        return 'Vertrag';
      case DocumentType.receipt:
        return 'Beleg';
      case DocumentType.warranty:
        return 'Garantie';
      case DocumentType.certificate:
        return 'Zertifikat';
      case DocumentType.other:
        return 'Sonstiges';
    }
  }

  String get statusText {
    switch (status) {
      case DocumentStatus.active:
        return 'Aktiv';
      case DocumentStatus.expired:
        return 'Abgelaufen';
      case DocumentStatus.pending:
        return 'Ausstehend';
      case DocumentStatus.archived:
        return 'Archiviert';
    }
  }

  DocumentModel copyWith({
    String? id,
    String? title,
    String? description,
    DocumentType? type,
    DocumentStatus? status,
    String? documentNumber,
    DateTime? issueDate,
    DateTime? expiryDate,
    String? issuingAuthority,
    List<String>? imageUrls,
    List<String>? tags,
    String? uploadedByUserId,
    String? familyId,
    List<String>? sharedWithUserIds,
    bool? isImportant,
    String? notes,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return DocumentModel(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      type: type ?? this.type,
      status: status ?? this.status,
      documentNumber: documentNumber ?? this.documentNumber,
      issueDate: issueDate ?? this.issueDate,
      expiryDate: expiryDate ?? this.expiryDate,
      issuingAuthority: issuingAuthority ?? this.issuingAuthority,
      imageUrls: imageUrls ?? this.imageUrls,
      tags: tags ?? this.tags,
      uploadedByUserId: uploadedByUserId ?? this.uploadedByUserId,
      familyId: familyId ?? this.familyId,
      sharedWithUserIds: sharedWithUserIds ?? this.sharedWithUserIds,
      isImportant: isImportant ?? this.isImportant,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'type': type.name,
      'status': status.name,
      'documentNumber': documentNumber,
      'issueDate': issueDate?.toIso8601String(),
      'expiryDate': expiryDate?.toIso8601String(),
      'issuingAuthority': issuingAuthority,
      'imageUrls': imageUrls,
      'tags': tags,
      'uploadedByUserId': uploadedByUserId,
      'familyId': familyId,
      'sharedWithUserIds': sharedWithUserIds,
      'isImportant': isImportant,
      'notes': notes,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  factory DocumentModel.fromJson(Map<String, dynamic> json) {
    return DocumentModel(
      id: json['id'] as String,
      title: json['title'] as String,
      description: json['description'] as String?,
      type: DocumentType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => DocumentType.other,
      ),
      status: DocumentStatus.values.firstWhere(
        (e) => e.name == json['status'],
        orElse: () => DocumentStatus.active,
      ),
      documentNumber: json['documentNumber'] as String?,
      issueDate: json['issueDate'] != null 
          ? DateTime.parse(json['issueDate'] as String) 
          : null,
      expiryDate: json['expiryDate'] != null 
          ? DateTime.parse(json['expiryDate'] as String) 
          : null,
      issuingAuthority: json['issuingAuthority'] as String?,
      imageUrls: List<String>.from(json['imageUrls'] as List? ?? []),
      tags: List<String>.from(json['tags'] as List? ?? []),
      uploadedByUserId: json['uploadedByUserId'] as String,
      familyId: json['familyId'] as String,
      sharedWithUserIds: List<String>.from(json['sharedWithUserIds'] as List? ?? []),
      isImportant: json['isImportant'] as bool? ?? false,
      notes: json['notes'] as String?,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
    );
  }

  @override
  List<Object?> get props => [
        id,
        title,
        description,
        type,
        status,
        documentNumber,
        issueDate,
        expiryDate,
        issuingAuthority,
        imageUrls,
        tags,
        uploadedByUserId,
        familyId,
        sharedWithUserIds,
        isImportant,
        notes,
        createdAt,
        updatedAt,
      ];
}

// Model for document scanning results
class ScannedDocumentModel extends Equatable {
  final String id;
  final String originalImagePath;
  final String? processedImagePath;
  final String? extractedText;
  final Map<String, dynamic>? extractedData;
  final DocumentType? detectedType;
  final double confidence;
  final DateTime scannedAt;

  const ScannedDocumentModel({
    required this.id,
    required this.originalImagePath,
    this.processedImagePath,
    this.extractedText,
    this.extractedData,
    this.detectedType,
    this.confidence = 0.0,
    required this.scannedAt,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'originalImagePath': originalImagePath,
      'processedImagePath': processedImagePath,
      'extractedText': extractedText,
      'extractedData': extractedData,
      'detectedType': detectedType?.name,
      'confidence': confidence,
      'scannedAt': scannedAt.toIso8601String(),
    };
  }

  factory ScannedDocumentModel.fromJson(Map<String, dynamic> json) {
    return ScannedDocumentModel(
      id: json['id'] as String,
      originalImagePath: json['originalImagePath'] as String,
      processedImagePath: json['processedImagePath'] as String?,
      extractedText: json['extractedText'] as String?,
      extractedData: json['extractedData'] as Map<String, dynamic>?,
      detectedType: json['detectedType'] != null
          ? DocumentType.values.firstWhere(
              (e) => e.name == json['detectedType'],
              orElse: () => DocumentType.other,
            )
          : null,
      confidence: (json['confidence'] as num?)?.toDouble() ?? 0.0,
      scannedAt: DateTime.parse(json['scannedAt'] as String),
    );
  }

  @override
  List<Object?> get props => [
        id,
        originalImagePath,
        processedImagePath,
        extractedText,
        extractedData,
        detectedType,
        confidence,
        scannedAt,
      ];
}
