import 'package:flutter/material.dart';
import '../../widgets/common/custom_card.dart';
import '../../../core/constants/app_constants.dart';

class TasksScreen extends StatelessWidget {
  const TasksScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Aufgaben'),
      ),
      body: const Center(
        child: CustomCard(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(Icons.task, size: 64),
              SizedBox(height: AppConstants.mediumSpacing),
              Text('Aufgaben Feature', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
              Text('Wird bald implementiert'),
            ],
          ),
        ),
      ),
    );
  }
}

class TaskDetailScreen extends StatelessWidget {
  final String taskId;
  
  const TaskDetailScreen({super.key, required this.taskId});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Aufgabe Details')),
      body: Center(child: Text('Task ID: $taskId')),
    );
  }
}

class CreateTaskScreen extends StatelessWidget {
  const CreateTaskScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Neue Aufgabe')),
      body: const Center(child: Text('Aufgabe erstellen')),
    );
  }
}
