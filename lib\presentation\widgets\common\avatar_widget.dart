import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../../../core/constants/app_constants.dart';
import '../../../core/theme/app_colors.dart';

enum AvatarSize { small, medium, large, extraLarge }

class AvatarWidget extends StatelessWidget {
  final String? imageUrl;
  final String? initials;
  final String? name;
  final AvatarSize size;
  final Color? backgroundColor;
  final Color? textColor;
  final VoidCallback? onTap;
  final bool showBorder;
  final Color? borderColor;
  final bool showOnlineIndicator;
  final bool isOnline;

  const AvatarWidget({
    super.key,
    this.imageUrl,
    this.initials,
    this.name,
    this.size = AvatarSize.medium,
    this.backgroundColor,
    this.textColor,
    this.onTap,
    this.showBorder = false,
    this.borderColor,
    this.showOnlineIndicator = false,
    this.isOnline = false,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    
    Widget avatar = _buildAvatar(context, theme, colorScheme);
    
    if (showOnlineIndicator) {
      avatar = _buildWithOnlineIndicator(avatar, colorScheme);
    }
    
    if (onTap != null) {
      avatar = InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(_getRadius()),
        child: avatar,
      );
    }
    
    return avatar;
  }

  Widget _buildAvatar(BuildContext context, ThemeData theme, ColorScheme colorScheme) {
    final radius = _getRadius();
    final fontSize = _getFontSize();
    
    return Container(
      width: _getSize(),
      height: _getSize(),
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        border: showBorder
            ? Border.all(
                color: borderColor ?? colorScheme.outline,
                width: 2,
              )
            : null,
      ),
      child: CircleAvatar(
        radius: radius,
        backgroundColor: backgroundColor ?? _getDefaultBackgroundColor(),
        backgroundImage: imageUrl != null ? _buildNetworkImage() : null,
        child: imageUrl == null ? _buildInitials(fontSize, colorScheme) : null,
      ),
    );
  }

  Widget _buildWithOnlineIndicator(Widget avatar, ColorScheme colorScheme) {
    return Stack(
      children: [
        avatar,
        Positioned(
          right: 0,
          bottom: 0,
          child: Container(
            width: _getIndicatorSize(),
            height: _getIndicatorSize(),
            decoration: BoxDecoration(
              color: isOnline ? AppColors.success : AppColors.grey400,
              shape: BoxShape.circle,
              border: Border.all(
                color: colorScheme.surface,
                width: 2,
              ),
            ),
          ),
        ),
      ],
    );
  }

  ImageProvider? _buildNetworkImage() {
    if (imageUrl == null || imageUrl!.isEmpty) return null;
    
    return CachedNetworkImageProvider(imageUrl!);
  }

  Widget? _buildInitials(double fontSize, ColorScheme colorScheme) {
    String displayInitials = '';
    
    if (initials != null && initials!.isNotEmpty) {
      displayInitials = initials!;
    } else if (name != null && name!.isNotEmpty) {
      final nameParts = name!.trim().split(' ');
      if (nameParts.length >= 2) {
        displayInitials = '${nameParts.first[0]}${nameParts.last[0]}';
      } else if (nameParts.isNotEmpty) {
        displayInitials = nameParts.first.length >= 2 
            ? nameParts.first.substring(0, 2)
            : nameParts.first[0];
      }
    }
    
    if (displayInitials.isEmpty) {
      return Icon(
        Icons.person,
        size: fontSize,
        color: textColor ?? colorScheme.onSurfaceVariant,
      );
    }
    
    return Text(
      displayInitials.toUpperCase(),
      style: TextStyle(
        fontSize: fontSize,
        fontWeight: FontWeight.w600,
        color: textColor ?? Colors.white,
      ),
    );
  }

  double _getSize() {
    switch (size) {
      case AvatarSize.small:
        return 32;
      case AvatarSize.medium:
        return 48;
      case AvatarSize.large:
        return 64;
      case AvatarSize.extraLarge:
        return 96;
    }
  }

  double _getRadius() {
    return _getSize() / 2;
  }

  double _getFontSize() {
    switch (size) {
      case AvatarSize.small:
        return 12;
      case AvatarSize.medium:
        return 16;
      case AvatarSize.large:
        return 20;
      case AvatarSize.extraLarge:
        return 32;
    }
  }

  double _getIndicatorSize() {
    switch (size) {
      case AvatarSize.small:
        return 8;
      case AvatarSize.medium:
        return 12;
      case AvatarSize.large:
        return 16;
      case AvatarSize.extraLarge:
        return 24;
    }
  }

  Color _getDefaultBackgroundColor() {
    if (backgroundColor != null) return backgroundColor!;
    
    // Generate a color based on the name or initials
    final text = name ?? initials ?? '';
    if (text.isEmpty) return AppColors.grey400;
    
    final hash = text.hashCode;
    final colorIndex = hash.abs() % AppColors.categoryColors.length;
    return AppColors.categoryColors[colorIndex];
  }
}

// Specialized avatar widgets
class UserAvatarWidget extends StatelessWidget {
  final String? imageUrl;
  final String firstName;
  final String lastName;
  final AvatarSize size;
  final VoidCallback? onTap;
  final bool showOnlineIndicator;
  final bool isOnline;

  const UserAvatarWidget({
    super.key,
    this.imageUrl,
    required this.firstName,
    required this.lastName,
    this.size = AvatarSize.medium,
    this.onTap,
    this.showOnlineIndicator = false,
    this.isOnline = false,
  });

  @override
  Widget build(BuildContext context) {
    final initials = '${firstName.isNotEmpty ? firstName[0] : ''}${lastName.isNotEmpty ? lastName[0] : ''}';
    final fullName = '$firstName $lastName';
    
    return AvatarWidget(
      imageUrl: imageUrl,
      initials: initials,
      name: fullName,
      size: size,
      onTap: onTap,
      showOnlineIndicator: showOnlineIndicator,
      isOnline: isOnline,
    );
  }
}

class GroupAvatarWidget extends StatelessWidget {
  final List<String> imageUrls;
  final AvatarSize size;
  final int maxVisible;
  final VoidCallback? onTap;

  const GroupAvatarWidget({
    super.key,
    required this.imageUrls,
    this.size = AvatarSize.medium,
    this.maxVisible = 3,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    
    if (imageUrls.isEmpty) {
      return AvatarWidget(
        size: size,
        onTap: onTap,
      );
    }
    
    if (imageUrls.length == 1) {
      return AvatarWidget(
        imageUrl: imageUrls.first,
        size: size,
        onTap: onTap,
      );
    }
    
    final visibleCount = imageUrls.length > maxVisible ? maxVisible : imageUrls.length;
    final remainingCount = imageUrls.length - maxVisible;
    
    return GestureDetector(
      onTap: onTap,
      child: SizedBox(
        width: _getGroupWidth(),
        height: _getSize(),
        child: Stack(
          children: [
            ...List.generate(visibleCount, (index) {
              final isLast = index == visibleCount - 1;
              final showCount = remainingCount > 0 && isLast;
              
              return Positioned(
                left: index * _getOffset(),
                child: Container(
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: colorScheme.surface,
                      width: 2,
                    ),
                  ),
                  child: showCount
                      ? CircleAvatar(
                          radius: _getRadius(),
                          backgroundColor: colorScheme.surfaceVariant,
                          child: Text(
                            '+$remainingCount',
                            style: TextStyle(
                              fontSize: _getFontSize() * 0.8,
                              fontWeight: FontWeight.w600,
                              color: colorScheme.onSurfaceVariant,
                            ),
                          ),
                        )
                      : AvatarWidget(
                          imageUrl: imageUrls[index],
                          size: size,
                        ),
                ),
              );
            }),
          ],
        ),
      ),
    );
  }

  double _getSize() {
    switch (size) {
      case AvatarSize.small:
        return 32;
      case AvatarSize.medium:
        return 48;
      case AvatarSize.large:
        return 64;
      case AvatarSize.extraLarge:
        return 96;
    }
  }

  double _getRadius() {
    return _getSize() / 2;
  }

  double _getFontSize() {
    switch (size) {
      case AvatarSize.small:
        return 12;
      case AvatarSize.medium:
        return 16;
      case AvatarSize.large:
        return 20;
      case AvatarSize.extraLarge:
        return 32;
    }
  }

  double _getOffset() {
    return _getSize() * 0.6;
  }

  double _getGroupWidth() {
    final visibleCount = imageUrls.length > maxVisible ? maxVisible : imageUrls.length;
    return _getSize() + ((visibleCount - 1) * _getOffset());
  }
}
