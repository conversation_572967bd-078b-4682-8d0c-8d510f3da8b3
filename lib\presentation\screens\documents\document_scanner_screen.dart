import 'package:flutter/material.dart';
import '../../widgets/common/custom_card.dart';
import '../../widgets/common/custom_button.dart';
import '../../../core/constants/app_constants.dart';
import '../../../core/theme/app_colors.dart';

class DocumentScannerScreen extends StatefulWidget {
  const DocumentScannerScreen({super.key});

  @override
  State<DocumentScannerScreen> createState() => _DocumentScannerScreenState();
}

class _DocumentScannerScreenState extends State<DocumentScannerScreen> {
  bool _isScanning = false;
  bool _hasScannedDocument = false;
  String? _scannedImagePath;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final size = MediaQuery.of(context).size;

    return Scaffold(
      appBar: AppBar(
        title: const Text('Dokument Scanner'),
        backgroundColor: Colors.black,
        foregroundColor: Colors.white,
        actions: [
          if (_hasScannedDocument)
            IconButton(
              icon: const Icon(Icons.check),
              onPressed: _saveDocument,
            ),
        ],
      ),
      backgroundColor: Colors.black,
      body: Stack(
        children: [
          // Camera Preview Placeholder
          Container(
            width: double.infinity,
            height: double.infinity,
            color: Colors.black,
            child: _hasScannedDocument
                ? _buildScannedPreview()
                : _buildCameraPreview(),
          ),
          
          // Overlay UI
          Positioned(
            top: 0,
            left: 0,
            right: 0,
            child: Container(
              padding: const EdgeInsets.all(AppConstants.largeSpacing),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Colors.black.withOpacity(0.7),
                    Colors.transparent,
                  ],
                ),
              ),
              child: SafeArea(
                child: Column(
                  children: [
                    Text(
                      _hasScannedDocument 
                          ? 'Dokument gescannt'
                          : 'Dokument in den Rahmen einpassen',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    if (!_hasScannedDocument) ...[
                      const SizedBox(height: AppConstants.smallSpacing),
                      Text(
                        'Halte das Dokument ruhig und achte auf gute Beleuchtung',
                        style: TextStyle(
                          color: Colors.white.withOpacity(0.8),
                          fontSize: 14,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ],
                ),
              ),
            ),
          ),
          
          // Bottom Controls
          Positioned(
            bottom: 0,
            left: 0,
            right: 0,
            child: Container(
              padding: const EdgeInsets.all(AppConstants.largeSpacing),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.bottomCenter,
                  end: Alignment.topCenter,
                  colors: [
                    Colors.black.withOpacity(0.8),
                    Colors.transparent,
                  ],
                ),
              ),
              child: SafeArea(
                child: _hasScannedDocument
                    ? _buildScannedControls()
                    : _buildCameraControls(),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCameraPreview() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Document Frame
          Container(
            width: 300,
            height: 200,
            decoration: BoxDecoration(
              border: Border.all(
                color: AppColors.primary,
                width: 3,
              ),
              borderRadius: BorderRadius.circular(AppConstants.mediumRadius),
            ),
            child: Stack(
              children: [
                // Corner indicators
                ...List.generate(4, (index) {
                  final isTop = index < 2;
                  final isLeft = index % 2 == 0;
                  
                  return Positioned(
                    top: isTop ? 8 : null,
                    bottom: !isTop ? 8 : null,
                    left: isLeft ? 8 : null,
                    right: !isLeft ? 8 : null,
                    child: Container(
                      width: 20,
                      height: 20,
                      decoration: BoxDecoration(
                        color: AppColors.primary,
                        borderRadius: BorderRadius.only(
                          topLeft: Radius.circular(isTop && isLeft ? 8 : 0),
                          topRight: Radius.circular(isTop && !isLeft ? 8 : 0),
                          bottomLeft: Radius.circular(!isTop && isLeft ? 8 : 0),
                          bottomRight: Radius.circular(!isTop && !isLeft ? 8 : 0),
                        ),
                      ),
                    ),
                  );
                }),
                
                // Center icon
                const Center(
                  child: Icon(
                    Icons.document_scanner,
                    size: 48,
                    color: Colors.white54,
                  ),
                ),
              ],
            ),
          ),
          
          const SizedBox(height: AppConstants.extraLargeSpacing),
          
          if (_isScanning)
            const Column(
              children: [
                CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
                ),
                SizedBox(height: AppConstants.mediumSpacing),
                Text(
                  'Dokument wird verarbeitet...',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                  ),
                ),
              ],
            ),
        ],
      ),
    );
  }

  Widget _buildScannedPreview() {
    return Container(
      margin: const EdgeInsets.all(AppConstants.largeSpacing),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppConstants.mediumRadius),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.3),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(AppConstants.mediumRadius),
        child: _scannedImagePath != null
            ? Image.asset(
                _scannedImagePath!,
                fit: BoxFit.contain,
                errorBuilder: (context, error, stackTrace) {
                  return _buildDocumentPlaceholder();
                },
              )
            : _buildDocumentPlaceholder(),
      ),
    );
  }

  Widget _buildDocumentPlaceholder() {
    return Container(
      width: double.infinity,
      height: 400,
      color: Colors.grey[100],
      child: const Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.description,
            size: 64,
            color: AppColors.grey400,
          ),
          SizedBox(height: AppConstants.mediumSpacing),
          Text(
            'Gescanntes Dokument',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: AppColors.grey600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCameraControls() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        // Flash toggle
        IconButton(
          onPressed: () {
            // TODO: Toggle flash
          },
          icon: const Icon(
            Icons.flash_off,
            color: Colors.white,
            size: 32,
          ),
        ),
        
        // Capture button
        GestureDetector(
          onTap: _isScanning ? null : _captureDocument,
          child: Container(
            width: 80,
            height: 80,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: _isScanning ? Colors.grey : AppColors.primary,
              border: Border.all(
                color: Colors.white,
                width: 4,
              ),
            ),
            child: Icon(
              _isScanning ? Icons.hourglass_empty : Icons.camera,
              color: Colors.white,
              size: 32,
            ),
          ),
        ),
        
        // Gallery button
        IconButton(
          onPressed: () {
            // TODO: Open gallery
          },
          icon: const Icon(
            Icons.photo_library,
            color: Colors.white,
            size: 32,
          ),
        ),
      ],
    );
  }

  Widget _buildScannedControls() {
    return Row(
      children: [
        Expanded(
          child: CustomButton(
            text: 'Erneut scannen',
            type: ButtonType.outline,
            customColor: Colors.white,
            customTextColor: Colors.white,
            onPressed: _retakePhoto,
          ),
        ),
        const SizedBox(width: AppConstants.mediumSpacing),
        Expanded(
          child: CustomButton(
            text: 'Speichern',
            customColor: AppColors.primary,
            onPressed: _saveDocument,
          ),
        ),
      ],
    );
  }

  void _captureDocument() async {
    setState(() {
      _isScanning = true;
    });

    // Simulate scanning process
    await Future.delayed(const Duration(seconds: 2));

    setState(() {
      _isScanning = false;
      _hasScannedDocument = true;
      _scannedImagePath = 'assets/images/sample_document.jpg'; // Placeholder
    });
  }

  void _retakePhoto() {
    setState(() {
      _hasScannedDocument = false;
      _scannedImagePath = null;
    });
  }

  void _saveDocument() {
    // TODO: Implement document saving
    Navigator.of(context).pop();
    
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Dokument gespeichert!'),
        backgroundColor: AppColors.success,
      ),
    );
  }
}
