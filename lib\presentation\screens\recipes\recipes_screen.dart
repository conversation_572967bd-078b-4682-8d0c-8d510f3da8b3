import 'package:flutter/material.dart';
import '../../widgets/common/custom_card.dart';
import '../../widgets/common/custom_text_field.dart';
import '../../widgets/common/custom_button.dart';
import '../../../core/constants/app_constants.dart';
import '../../../core/theme/app_colors.dart';
import '../../../data/models/recipe_model.dart';

class RecipesScreen extends StatefulWidget {
  const RecipesScreen({super.key});

  @override
  State<RecipesScreen> createState() => _RecipesScreenState();
}

class _RecipesScreenState extends State<RecipesScreen> with TickerProviderStateMixin {
  late TabController _tabController;
  final TextEditingController _searchController = TextEditingController();
  
  // Mock data
  final List<RecipeModel> _mockRecipes = [
    RecipeModel(
      id: 'recipe_1',
      title: 'Spaghetti Bolognese',
      description: 'Klassische italienische Pasta mit Fleischsauce',
      category: RecipeCategory.dinner,
      difficulty: RecipeDifficulty.medium,
      preparationTimeMinutes: 20,
      cookingTimeMinutes: 45,
      servings: 4,
      rating: 4.5,
      ratingCount: 12,
      createdByUserId: 'user_1',
      familyId: 'family_1',
      isFavorite: true,
      tags: ['Pasta', 'Italienisch', 'Fleisch'],
      createdAt: DateTime.now().subtract(const Duration(days: 5)),
      updatedAt: DateTime.now().subtract(const Duration(days: 5)),
    ),
    RecipeModel(
      id: 'recipe_2',
      title: 'Gemüsecurry',
      description: 'Würziges vegetarisches Curry mit Kokosmilch',
      category: RecipeCategory.vegetarian,
      difficulty: RecipeDifficulty.easy,
      preparationTimeMinutes: 15,
      cookingTimeMinutes: 30,
      servings: 3,
      rating: 4.2,
      ratingCount: 8,
      createdByUserId: 'user_2',
      familyId: 'family_1',
      tags: ['Vegetarisch', 'Curry', 'Gesund'],
      createdAt: DateTime.now().subtract(const Duration(days: 2)),
      updatedAt: DateTime.now().subtract(const Duration(days: 2)),
    ),
    RecipeModel(
      id: 'recipe_3',
      title: 'Schokoladenkuchen',
      description: 'Saftiger Schokoladenkuchen für besondere Anlässe',
      category: RecipeCategory.dessert,
      difficulty: RecipeDifficulty.hard,
      preparationTimeMinutes: 30,
      cookingTimeMinutes: 50,
      servings: 8,
      rating: 4.8,
      ratingCount: 15,
      createdByUserId: 'user_1',
      familyId: 'family_1',
      isFavorite: true,
      tags: ['Dessert', 'Schokolade', 'Kuchen'],
      createdAt: DateTime.now().subtract(const Duration(days: 1)),
      updatedAt: DateTime.now().subtract(const Duration(days: 1)),
    ),
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Rezepte'),
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'Alle', icon: Icon(Icons.restaurant)),
            Tab(text: 'Favoriten', icon: Icon(Icons.favorite)),
            Tab(text: 'Kategorien', icon: Icon(Icons.category)),
          ],
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () {
              _showCreateRecipeDialog(context);
            },
          ),
        ],
      ),
      body: Column(
        children: [
          // Search Bar
          Padding(
            padding: const EdgeInsets.all(AppConstants.mediumSpacing),
            child: SearchTextField(
              hint: 'Rezepte durchsuchen...',
              controller: _searchController,
              onChanged: (value) {
                // TODO: Implement search
              },
            ),
          ),
          
          // Tab Content
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildAllRecipes(),
                _buildFavoriteRecipes(),
                _buildCategoriesView(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAllRecipes() {
    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: AppConstants.mediumSpacing),
      itemCount: _mockRecipes.length,
      itemBuilder: (context, index) {
        final recipe = _mockRecipes[index];
        return Padding(
          padding: const EdgeInsets.only(bottom: AppConstants.mediumSpacing),
          child: _buildRecipeCard(recipe),
        );
      },
    );
  }

  Widget _buildFavoriteRecipes() {
    final favoriteRecipes = _mockRecipes.where((recipe) => recipe.isFavorite).toList();
    
    if (favoriteRecipes.isEmpty) {
      return const Center(
        child: CustomCard(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(Icons.favorite_border, size: 64, color: AppColors.grey400),
              SizedBox(height: AppConstants.mediumSpacing),
              Text('Keine Favoriten', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
              Text('Markiere Rezepte als Favoriten, um sie hier zu sehen'),
            ],
          ),
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: AppConstants.mediumSpacing),
      itemCount: favoriteRecipes.length,
      itemBuilder: (context, index) {
        final recipe = favoriteRecipes[index];
        return Padding(
          padding: const EdgeInsets.only(bottom: AppConstants.mediumSpacing),
          child: _buildRecipeCard(recipe),
        );
      },
    );
  }

  Widget _buildCategoriesView() {
    final categories = RecipeCategory.values;
    
    return GridView.builder(
      padding: const EdgeInsets.all(AppConstants.mediumSpacing),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: AppConstants.mediumSpacing,
        mainAxisSpacing: AppConstants.mediumSpacing,
        childAspectRatio: 1.2,
      ),
      itemCount: categories.length,
      itemBuilder: (context, index) {
        final category = categories[index];
        final categoryRecipes = _mockRecipes.where((r) => r.category == category).length;
        
        return CustomCard(
          onTap: () {
            // TODO: Navigate to category recipes
          },
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                _getCategoryIcon(category),
                size: AppConstants.extraLargeIcon,
                color: AppColors.categoryColors[index % AppColors.categoryColors.length],
              ),
              const SizedBox(height: AppConstants.smallSpacing),
              Text(
                _getCategoryText(category),
                style: const TextStyle(
                  fontWeight: FontWeight.w600,
                ),
                textAlign: TextAlign.center,
              ),
              Text(
                '$categoryRecipes Rezepte',
                style: TextStyle(
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                  fontSize: 12,
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildRecipeCard(RecipeModel recipe) {
    final theme = Theme.of(context);
    
    return CustomCard(
      onTap: () {
        // TODO: Navigate to recipe detail
      },
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Recipe Image Placeholder
          Container(
            height: 150,
            width: double.infinity,
            decoration: BoxDecoration(
              color: AppColors.grey200,
              borderRadius: BorderRadius.circular(AppConstants.smallRadius),
            ),
            child: recipe.imageUrl != null
                ? ClipRRect(
                    borderRadius: BorderRadius.circular(AppConstants.smallRadius),
                    child: Image.network(
                      recipe.imageUrl!,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) {
                        return _buildImagePlaceholder();
                      },
                    ),
                  )
                : _buildImagePlaceholder(),
          ),
          
          const SizedBox(height: AppConstants.mediumSpacing),
          
          // Recipe Info
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      recipe.title,
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    if (recipe.description != null) ...[
                      const SizedBox(height: 4),
                      Text(
                        recipe.description!,
                        style: theme.textTheme.bodyMedium?.copyWith(
                          color: theme.colorScheme.onSurfaceVariant,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ],
                ),
              ),
              IconButton(
                icon: Icon(
                  recipe.isFavorite ? Icons.favorite : Icons.favorite_border,
                  color: recipe.isFavorite ? AppColors.error : null,
                ),
                onPressed: () {
                  // TODO: Toggle favorite
                },
              ),
            ],
          ),
          
          const SizedBox(height: AppConstants.smallSpacing),
          
          // Recipe Meta
          Row(
            children: [
              _buildMetaChip(
                icon: Icons.access_time,
                text: '${recipe.totalTimeMinutes} Min',
              ),
              const SizedBox(width: AppConstants.smallSpacing),
              _buildMetaChip(
                icon: Icons.people,
                text: '${recipe.servings} Portionen',
              ),
              const SizedBox(width: AppConstants.smallSpacing),
              _buildMetaChip(
                icon: Icons.signal_cellular_alt,
                text: recipe.difficultyText,
              ),
            ],
          ),
          
          if (recipe.rating != null) ...[
            const SizedBox(height: AppConstants.smallSpacing),
            Row(
              children: [
                ...List.generate(5, (index) {
                  return Icon(
                    index < recipe.rating!.floor()
                        ? Icons.star
                        : index < recipe.rating!
                            ? Icons.star_half
                            : Icons.star_border,
                    size: 16,
                    color: AppColors.warning,
                  );
                }),
                const SizedBox(width: AppConstants.smallSpacing),
                Text(
                  '${recipe.rating!.toStringAsFixed(1)} (${recipe.ratingCount})',
                  style: theme.textTheme.bodySmall,
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildImagePlaceholder() {
    return Container(
      decoration: BoxDecoration(
        color: AppColors.grey200,
        borderRadius: BorderRadius.circular(AppConstants.smallRadius),
      ),
      child: const Center(
        child: Icon(
          Icons.restaurant,
          size: 48,
          color: AppColors.grey400,
        ),
      ),
    );
  }

  Widget _buildMetaChip({required IconData icon, required String text}) {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppConstants.smallSpacing,
        vertical: 4,
      ),
      decoration: BoxDecoration(
        color: AppColors.grey100,
        borderRadius: BorderRadius.circular(AppConstants.smallRadius),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 14, color: AppColors.grey600),
          const SizedBox(width: 4),
          Text(
            text,
            style: const TextStyle(
              fontSize: 12,
              color: AppColors.grey600,
            ),
          ),
        ],
      ),
    );
  }

  IconData _getCategoryIcon(RecipeCategory category) {
    switch (category) {
      case RecipeCategory.breakfast:
        return Icons.free_breakfast;
      case RecipeCategory.lunch:
        return Icons.lunch_dining;
      case RecipeCategory.dinner:
        return Icons.dinner_dining;
      case RecipeCategory.snack:
        return Icons.cookie;
      case RecipeCategory.dessert:
        return Icons.cake;
      case RecipeCategory.drink:
        return Icons.local_drink;
      case RecipeCategory.vegetarian:
        return Icons.eco;
      case RecipeCategory.vegan:
        return Icons.grass;
      case RecipeCategory.glutenFree:
        return Icons.no_food;
      case RecipeCategory.other:
        return Icons.restaurant;
    }
  }

  String _getCategoryText(RecipeCategory category) {
    switch (category) {
      case RecipeCategory.breakfast:
        return 'Frühstück';
      case RecipeCategory.lunch:
        return 'Mittagessen';
      case RecipeCategory.dinner:
        return 'Abendessen';
      case RecipeCategory.snack:
        return 'Snack';
      case RecipeCategory.dessert:
        return 'Dessert';
      case RecipeCategory.drink:
        return 'Getränke';
      case RecipeCategory.vegetarian:
        return 'Vegetarisch';
      case RecipeCategory.vegan:
        return 'Vegan';
      case RecipeCategory.glutenFree:
        return 'Glutenfrei';
      case RecipeCategory.other:
        return 'Sonstiges';
    }
  }

  void _showCreateRecipeDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Neues Rezept'),
        content: const Text('Rezept erstellen Feature kommt bald!'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }
}
