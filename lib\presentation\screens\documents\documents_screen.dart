import 'package:flutter/material.dart';
import '../../widgets/common/custom_card.dart';
import '../../widgets/common/custom_text_field.dart';
import '../../widgets/common/custom_button.dart';
import '../../../core/constants/app_constants.dart';
import '../../../core/theme/app_colors.dart';
import '../../../data/models/document_model.dart';

class DocumentsScreen extends StatefulWidget {
  const DocumentsScreen({super.key});

  @override
  State<DocumentsScreen> createState() => _DocumentsScreenState();
}

class _DocumentsScreenState extends State<DocumentsScreen> with TickerProviderStateMixin {
  late TabController _tabController;
  final TextEditingController _searchController = TextEditingController();
  
  // Mock data
  final List<DocumentModel> _mockDocuments = [
    DocumentModel(
      id: 'doc_1',
      title: 'Personalausweis Max',
      description: '<PERSON><PERSON><PERSON><PERSON> <PERSON>',
      type: DocumentType.idCard,
      status: DocumentStatus.active,
      documentNumber: 'T22000126',
      issueDate: DateTime(2020, 3, 15),
      expiryDate: DateTime(2030, 3, 14),
      issuingAuthority: 'Stadt München',
      uploadedByUserId: 'user_1',
      familyId: 'family_1',
      isImportant: true,
      tags: ['Ausweis', 'Wichtig'],
      createdAt: DateTime.now().subtract(const Duration(days: 30)),
      updatedAt: DateTime.now().subtract(const Duration(days: 30)),
    ),
    DocumentModel(
      id: 'doc_2',
      title: 'Krankenversicherung Familie',
      description: 'Versicherungspolice der Familienversicherung',
      type: DocumentType.insurance,
      status: DocumentStatus.active,
      documentNumber: 'KV-2024-001',
      issueDate: DateTime(2024, 1, 1),
      expiryDate: DateTime(2024, 12, 31),
      issuingAuthority: 'AOK Bayern',
      uploadedByUserId: 'user_2',
      familyId: 'family_1',
      sharedWithUserIds: ['user_1', 'user_3'],
      isImportant: true,
      tags: ['Versicherung', 'Gesundheit'],
      createdAt: DateTime.now().subtract(const Duration(days: 10)),
      updatedAt: DateTime.now().subtract(const Duration(days: 10)),
    ),
    DocumentModel(
      id: 'doc_3',
      title: 'Zeugnis Tim 2024',
      description: 'Halbjahreszeugnis 4. Klasse',
      type: DocumentType.school,
      status: DocumentStatus.active,
      issueDate: DateTime(2024, 2, 1),
      issuingAuthority: 'Grundschule München',
      uploadedByUserId: 'user_1',
      familyId: 'family_1',
      tags: ['Schule', 'Zeugnis'],
      createdAt: DateTime.now().subtract(const Duration(days: 5)),
      updatedAt: DateTime.now().subtract(const Duration(days: 5)),
    ),
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Dokumente'),
        bottom: TabBar(
          controller: _tabController,
          isScrollable: true,
          tabs: const [
            Tab(text: 'Alle', icon: Icon(Icons.folder)),
            Tab(text: 'Wichtig', icon: Icon(Icons.star)),
            Tab(text: 'Ablaufend', icon: Icon(Icons.warning)),
            Tab(text: 'Typen', icon: Icon(Icons.category)),
          ],
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.document_scanner),
            onPressed: () {
              _showScannerOptions(context);
            },
          ),
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () {
              _showAddDocumentDialog(context);
            },
          ),
        ],
      ),
      body: Column(
        children: [
          // Search Bar
          Padding(
            padding: const EdgeInsets.all(AppConstants.mediumSpacing),
            child: SearchTextField(
              hint: 'Dokumente durchsuchen...',
              controller: _searchController,
              onChanged: (value) {
                // TODO: Implement search
              },
            ),
          ),
          
          // Tab Content
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildAllDocuments(),
                _buildImportantDocuments(),
                _buildExpiringDocuments(),
                _buildDocumentTypes(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAllDocuments() {
    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: AppConstants.mediumSpacing),
      itemCount: _mockDocuments.length,
      itemBuilder: (context, index) {
        final document = _mockDocuments[index];
        return Padding(
          padding: const EdgeInsets.only(bottom: AppConstants.mediumSpacing),
          child: _buildDocumentCard(document),
        );
      },
    );
  }

  Widget _buildImportantDocuments() {
    final importantDocs = _mockDocuments.where((doc) => doc.isImportant).toList();
    
    if (importantDocs.isEmpty) {
      return const Center(
        child: CustomCard(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(Icons.star_border, size: 64, color: AppColors.grey400),
              SizedBox(height: AppConstants.mediumSpacing),
              Text('Keine wichtigen Dokumente', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
              Text('Markiere Dokumente als wichtig, um sie hier zu sehen'),
            ],
          ),
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: AppConstants.mediumSpacing),
      itemCount: importantDocs.length,
      itemBuilder: (context, index) {
        final document = importantDocs[index];
        return Padding(
          padding: const EdgeInsets.only(bottom: AppConstants.mediumSpacing),
          child: _buildDocumentCard(document),
        );
      },
    );
  }

  Widget _buildExpiringDocuments() {
    final expiringDocs = _mockDocuments.where((doc) => doc.isExpiringSoon || doc.isExpired).toList();
    
    if (expiringDocs.isEmpty) {
      return const Center(
        child: CustomCard(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(Icons.check_circle, size: 64, color: AppColors.success),
              SizedBox(height: AppConstants.mediumSpacing),
              Text('Alle Dokumente aktuell', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
              Text('Keine Dokumente laufen in den nächsten 30 Tagen ab'),
            ],
          ),
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: AppConstants.mediumSpacing),
      itemCount: expiringDocs.length,
      itemBuilder: (context, index) {
        final document = expiringDocs[index];
        return Padding(
          padding: const EdgeInsets.only(bottom: AppConstants.mediumSpacing),
          child: _buildDocumentCard(document),
        );
      },
    );
  }

  Widget _buildDocumentTypes() {
    final types = DocumentType.values;
    
    return GridView.builder(
      padding: const EdgeInsets.all(AppConstants.mediumSpacing),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: AppConstants.mediumSpacing,
        mainAxisSpacing: AppConstants.mediumSpacing,
        childAspectRatio: 1.2,
      ),
      itemCount: types.length,
      itemBuilder: (context, index) {
        final type = types[index];
        final typeDocuments = _mockDocuments.where((d) => d.type == type).length;
        
        return CustomCard(
          onTap: () {
            // TODO: Navigate to type documents
          },
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                _getDocumentTypeIcon(type),
                size: AppConstants.extraLargeIcon,
                color: AppColors.categoryColors[index % AppColors.categoryColors.length],
              ),
              const SizedBox(height: AppConstants.smallSpacing),
              Text(
                _getDocumentTypeText(type),
                style: const TextStyle(
                  fontWeight: FontWeight.w600,
                ),
                textAlign: TextAlign.center,
              ),
              Text(
                '$typeDocuments Dokumente',
                style: TextStyle(
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                  fontSize: 12,
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildDocumentCard(DocumentModel document) {
    final theme = Theme.of(context);
    
    return CustomCard(
      onTap: () {
        // TODO: Navigate to document detail
      },
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(AppConstants.smallSpacing),
                decoration: BoxDecoration(
                  color: _getDocumentTypeColor(document.type).withOpacity(0.1),
                  borderRadius: BorderRadius.circular(AppConstants.smallRadius),
                ),
                child: Icon(
                  _getDocumentTypeIcon(document.type),
                  color: _getDocumentTypeColor(document.type),
                  size: AppConstants.mediumIcon,
                ),
              ),
              const SizedBox(width: AppConstants.mediumSpacing),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            document.title,
                            style: theme.textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                        if (document.isImportant)
                          const Icon(
                            Icons.star,
                            color: AppColors.warning,
                            size: 20,
                          ),
                      ],
                    ),
                    if (document.description != null) ...[
                      const SizedBox(height: 4),
                      Text(
                        document.description!,
                        style: theme.textTheme.bodyMedium?.copyWith(
                          color: theme.colorScheme.onSurfaceVariant,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ],
                ),
              ),
            ],
          ),
          
          const SizedBox(height: AppConstants.mediumSpacing),
          
          // Document Meta
          Wrap(
            spacing: AppConstants.smallSpacing,
            runSpacing: AppConstants.smallSpacing,
            children: [
              _buildMetaChip(
                text: document.typeText,
                color: _getDocumentTypeColor(document.type),
              ),
              if (document.expiryDate != null)
                _buildMetaChip(
                  text: 'Gültig bis ${_formatDate(document.expiryDate!)}',
                  color: document.isExpired 
                      ? AppColors.error 
                      : document.isExpiringSoon 
                          ? AppColors.warning 
                          : AppColors.success,
                ),
              if (document.documentNumber != null)
                _buildMetaChip(
                  text: document.documentNumber!,
                  color: AppColors.grey500,
                ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildMetaChip({required String text, required Color color}) {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppConstants.smallSpacing,
        vertical: 4,
      ),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(AppConstants.smallRadius),
      ),
      child: Text(
        text,
        style: TextStyle(
          fontSize: 12,
          color: color,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  IconData _getDocumentTypeIcon(DocumentType type) {
    switch (type) {
      case DocumentType.passport:
        return Icons.flight;
      case DocumentType.idCard:
        return Icons.badge;
      case DocumentType.drivingLicense:
        return Icons.drive_eta;
      case DocumentType.insurance:
        return Icons.security;
      case DocumentType.medical:
        return Icons.medical_services;
      case DocumentType.school:
        return Icons.school;
      case DocumentType.work:
        return Icons.work;
      case DocumentType.contract:
        return Icons.description;
      case DocumentType.receipt:
        return Icons.receipt;
      case DocumentType.warranty:
        return Icons.verified;
      case DocumentType.certificate:
        return Icons.workspace_premium;
      case DocumentType.other:
        return Icons.insert_drive_file;
    }
  }

  String _getDocumentTypeText(DocumentType type) {
    switch (type) {
      case DocumentType.passport:
        return 'Reisepass';
      case DocumentType.idCard:
        return 'Personalausweis';
      case DocumentType.drivingLicense:
        return 'Führerschein';
      case DocumentType.insurance:
        return 'Versicherung';
      case DocumentType.medical:
        return 'Medizinisch';
      case DocumentType.school:
        return 'Schule';
      case DocumentType.work:
        return 'Arbeit';
      case DocumentType.contract:
        return 'Vertrag';
      case DocumentType.receipt:
        return 'Beleg';
      case DocumentType.warranty:
        return 'Garantie';
      case DocumentType.certificate:
        return 'Zertifikat';
      case DocumentType.other:
        return 'Sonstiges';
    }
  }

  Color _getDocumentTypeColor(DocumentType type) {
    final index = DocumentType.values.indexOf(type);
    return AppColors.categoryColors[index % AppColors.categoryColors.length];
  }

  String _formatDate(DateTime date) {
    return '${date.day}.${date.month}.${date.year}';
  }

  void _showScannerOptions(BuildContext context) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(AppConstants.largeSpacing),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'Dokument scannen',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: AppConstants.largeSpacing),
            CustomButton(
              text: 'Kamera öffnen',
              icon: Icons.camera_alt,
              onPressed: () {
                Navigator.pop(context);
                // TODO: Open camera scanner
              },
              isFullWidth: true,
            ),
            const SizedBox(height: AppConstants.mediumSpacing),
            CustomButton(
              text: 'Aus Galerie wählen',
              icon: Icons.photo_library,
              type: ButtonType.outline,
              onPressed: () {
                Navigator.pop(context);
                // TODO: Open gallery picker
              },
              isFullWidth: true,
            ),
          ],
        ),
      ),
    );
  }

  void _showAddDocumentDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Neues Dokument'),
        content: const Text('Dokument hinzufügen Feature kommt bald!'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }
}
