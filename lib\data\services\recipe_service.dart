import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:uuid/uuid.dart';
import '../models/recipe_model.dart';

class RecipeService {
  static const String _recipesKey = 'recipes_data';
  
  final Uuid _uuid = const Uuid();

  // Mock recipe data
  static final List<Map<String, dynamic>> _mockRecipes = [
    {
      'id': 'recipe_1',
      'title': 'Spaghetti Bolognese',
      'description': 'Klassische italienische Pasta mit Fleischsauce',
      'imageUrl': null,
      'category': 'dinner',
      'difficulty': 'medium',
      'preparationTimeMinutes': 20,
      'cookingTimeMinutes': 45,
      'servings': 4,
      'ingredients': [
        {
          'name': 'Spaghetti',
          'amount': 400.0,
          'unit': 'g',
          'notes': null,
        },
        {
          'name': 'Hackfleisch',
          'amount': 500.0,
          'unit': 'g',
          'notes': 'gemischt',
        },
        {
          'name': 'Zwiebeln',
          'amount': 2.0,
          'unit': 'Stück',
          'notes': null,
        },
        {
          'name': 'Tomaten',
          'amount': 400.0,
          'unit': 'g',
          'notes': 'gehackt aus der Dose',
        },
      ],
      'steps': [
        {
          'stepNumber': 1,
          'instruction': 'Zwiebeln schälen und fein würfeln.',
          'timeMinutes': 5,
          'imageUrl': null,
        },
        {
          'stepNumber': 2,
          'instruction': 'Hackfleisch in einer Pfanne anbraten.',
          'timeMinutes': 10,
          'imageUrl': null,
        },
        {
          'stepNumber': 3,
          'instruction': 'Zwiebeln hinzufügen und glasig dünsten.',
          'timeMinutes': 5,
          'imageUrl': null,
        },
        {
          'stepNumber': 4,
          'instruction': 'Tomaten hinzufügen und 30 Minuten köcheln lassen.',
          'timeMinutes': 30,
          'imageUrl': null,
        },
        {
          'stepNumber': 5,
          'instruction': 'Spaghetti nach Packungsanweisung kochen.',
          'timeMinutes': 10,
          'imageUrl': null,
        },
      ],
      'tags': ['Pasta', 'Italienisch', 'Fleisch'],
      'rating': 4.5,
      'ratingCount': 12,
      'createdByUserId': 'user_1',
      'familyId': 'family_1',
      'isFavorite': true,
      'nutritionInfo': null,
      'createdAt': '2024-01-01T00:00:00.000Z',
      'updatedAt': '2024-01-01T00:00:00.000Z',
    },
  ];

  Future<List<RecipeModel>> getRecipesByFamily(String familyId) async {
    await Future.delayed(const Duration(milliseconds: 300));
    
    try {
      final recipes = await _getRecipes();
      final familyRecipes = recipes
          .where((recipe) => recipe['familyId'] == familyId)
          .map((recipeMap) => RecipeModel.fromJson(recipeMap))
          .toList();
      
      // Sort by creation date (newest first)
      familyRecipes.sort((a, b) => b.createdAt.compareTo(a.createdAt));
      
      return familyRecipes;
    } catch (e) {
      throw Exception('Failed to get family recipes: $e');
    }
  }

  Future<List<RecipeModel>> getRecipesByCategory(String familyId, RecipeCategory category) async {
    await Future.delayed(const Duration(milliseconds: 300));
    
    try {
      final recipes = await getRecipesByFamily(familyId);
      return recipes.where((recipe) => recipe.category == category).toList();
    } catch (e) {
      throw Exception('Failed to get recipes by category: $e');
    }
  }

  Future<List<RecipeModel>> getFavoriteRecipes(String familyId) async {
    await Future.delayed(const Duration(milliseconds: 300));
    
    try {
      final recipes = await getRecipesByFamily(familyId);
      return recipes.where((recipe) => recipe.isFavorite).toList();
    } catch (e) {
      throw Exception('Failed to get favorite recipes: $e');
    }
  }

  Future<RecipeModel?> createRecipe({
    required String title,
    String? description,
    String? imageUrl,
    RecipeCategory category = RecipeCategory.other,
    RecipeDifficulty difficulty = RecipeDifficulty.medium,
    required int preparationTimeMinutes,
    required int cookingTimeMinutes,
    required int servings,
    List<RecipeIngredient> ingredients = const [],
    List<RecipeStep> steps = const [],
    List<String> tags = const [],
    required String createdByUserId,
    required String familyId,
    String? nutritionInfo,
  }) async {
    await Future.delayed(const Duration(milliseconds: 500));
    
    try {
      final recipes = await _getRecipes();
      final now = DateTime.now();
      
      final newRecipe = RecipeModel(
        id: _uuid.v4(),
        title: title,
        description: description,
        imageUrl: imageUrl,
        category: category,
        difficulty: difficulty,
        preparationTimeMinutes: preparationTimeMinutes,
        cookingTimeMinutes: cookingTimeMinutes,
        servings: servings,
        ingredients: ingredients,
        steps: steps,
        tags: tags,
        createdByUserId: createdByUserId,
        familyId: familyId,
        nutritionInfo: nutritionInfo,
        createdAt: now,
        updatedAt: now,
      );
      
      recipes.add(newRecipe.toJson());
      await _saveRecipes(recipes);
      
      return newRecipe;
    } catch (e) {
      throw Exception('Failed to create recipe: $e');
    }
  }

  Future<RecipeModel?> updateRecipe(RecipeModel recipe) async {
    await Future.delayed(const Duration(milliseconds: 500));
    
    try {
      final recipes = await _getRecipes();
      final recipeIndex = recipes.indexWhere((r) => r['id'] == recipe.id);
      
      if (recipeIndex != -1) {
        final updatedRecipe = recipe.copyWith(updatedAt: DateTime.now());
        recipes[recipeIndex] = updatedRecipe.toJson();
        await _saveRecipes(recipes);
        return updatedRecipe;
      }
      return null;
    } catch (e) {
      throw Exception('Failed to update recipe: $e');
    }
  }

  Future<bool> toggleFavorite(String recipeId) async {
    await Future.delayed(const Duration(milliseconds: 300));
    
    try {
      final recipes = await _getRecipes();
      final recipeIndex = recipes.indexWhere((recipe) => recipe['id'] == recipeId);
      
      if (recipeIndex != -1) {
        final recipeMap = recipes[recipeIndex];
        recipeMap['isFavorite'] = !(recipeMap['isFavorite'] as bool? ?? false);
        recipeMap['updatedAt'] = DateTime.now().toIso8601String();
        
        recipes[recipeIndex] = recipeMap;
        await _saveRecipes(recipes);
        return true;
      }
      return false;
    } catch (e) {
      throw Exception('Failed to toggle favorite: $e');
    }
  }

  Future<bool> rateRecipe(String recipeId, double rating) async {
    await Future.delayed(const Duration(milliseconds: 300));
    
    try {
      final recipes = await _getRecipes();
      final recipeIndex = recipes.indexWhere((recipe) => recipe['id'] == recipeId);
      
      if (recipeIndex != -1) {
        final recipeMap = recipes[recipeIndex];
        final currentRating = recipeMap['rating'] as double?;
        final currentCount = recipeMap['ratingCount'] as int? ?? 0;
        
        double newRating;
        int newCount;
        
        if (currentRating == null) {
          newRating = rating;
          newCount = 1;
        } else {
          newCount = currentCount + 1;
          newRating = ((currentRating * currentCount) + rating) / newCount;
        }
        
        recipeMap['rating'] = newRating;
        recipeMap['ratingCount'] = newCount;
        recipeMap['updatedAt'] = DateTime.now().toIso8601String();
        
        recipes[recipeIndex] = recipeMap;
        await _saveRecipes(recipes);
        return true;
      }
      return false;
    } catch (e) {
      throw Exception('Failed to rate recipe: $e');
    }
  }

  Future<bool> deleteRecipe(String recipeId) async {
    await Future.delayed(const Duration(milliseconds: 500));
    
    try {
      final recipes = await _getRecipes();
      final recipeIndex = recipes.indexWhere((recipe) => recipe['id'] == recipeId);
      
      if (recipeIndex != -1) {
        recipes.removeAt(recipeIndex);
        await _saveRecipes(recipes);
        return true;
      }
      return false;
    } catch (e) {
      throw Exception('Failed to delete recipe: $e');
    }
  }

  Future<RecipeModel?> getRecipeById(String recipeId) async {
    await Future.delayed(const Duration(milliseconds: 200));
    
    try {
      final recipes = await _getRecipes();
      final recipeMap = recipes.firstWhere(
        (recipe) => recipe['id'] == recipeId,
        orElse: () => <String, dynamic>{},
      );
      
      if (recipeMap.isNotEmpty) {
        return RecipeModel.fromJson(recipeMap);
      }
      return null;
    } catch (e) {
      throw Exception('Failed to get recipe: $e');
    }
  }

  Future<List<RecipeModel>> searchRecipes(String familyId, String query) async {
    await Future.delayed(const Duration(milliseconds: 300));
    
    try {
      final recipes = await getRecipesByFamily(familyId);
      final lowercaseQuery = query.toLowerCase();
      
      return recipes.where((recipe) {
        return recipe.title.toLowerCase().contains(lowercaseQuery) ||
               (recipe.description?.toLowerCase().contains(lowercaseQuery) ?? false) ||
               recipe.tags.any((tag) => tag.toLowerCase().contains(lowercaseQuery));
      }).toList();
    } catch (e) {
      throw Exception('Failed to search recipes: $e');
    }
  }

  Future<List<Map<String, dynamic>>> _getRecipes() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final recipesJson = prefs.getString(_recipesKey);
      
      if (recipesJson != null) {
        final recipesList = jsonDecode(recipesJson) as List;
        return recipesList.cast<Map<String, dynamic>>();
      } else {
        await _saveRecipes(_mockRecipes);
        return List<Map<String, dynamic>>.from(_mockRecipes);
      }
    } catch (e) {
      return List<Map<String, dynamic>>.from(_mockRecipes);
    }
  }

  Future<void> _saveRecipes(List<Map<String, dynamic>> recipes) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final recipesJson = jsonEncode(recipes);
      await prefs.setString(_recipesKey, recipesJson);
    } catch (e) {
      throw Exception('Failed to save recipes: $e');
    }
  }
}
