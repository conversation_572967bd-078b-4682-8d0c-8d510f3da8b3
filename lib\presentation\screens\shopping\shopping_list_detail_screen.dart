import 'package:flutter/material.dart';
import '../../widgets/common/custom_card.dart';
import '../../../core/constants/app_constants.dart';

class ShoppingListDetailScreen extends StatelessWidget {
  final String listId;
  
  const ShoppingListDetailScreen({super.key, required this.listId});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Einkaufsliste Details')),
      body: Center(
        child: CustomCard(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Icon(Icons.shopping_cart, size: 64),
              const SizedBox(height: AppConstants.mediumSpacing),
              const Text('Einkaufsliste Details', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
              Text('List ID: $listId'),
              const Text('Wird bald implementiert'),
            ],
          ),
        ),
      ),
    );
  }
}
