import 'package:flutter/material.dart';
import '../../widgets/common/custom_card.dart';
import '../../../core/constants/app_constants.dart';

class ContactsScreen extends StatelessWidget {
  const ContactsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Familie'),
      ),
      body: const Center(
        child: CustomCard(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(Icons.people, size: 64),
              SizedBox(height: AppConstants.mediumSpacing),
              Text('Familie Feature',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
              Text('Wird bald implementiert'),
            ],
          ),
        ),
      ),
    );
  }
}
