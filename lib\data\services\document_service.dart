import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:uuid/uuid.dart';
import '../models/document_model.dart';

class DocumentService {
  static const String _documentsKey = 'documents_data';
  
  final Uuid _uuid = const Uuid();

  // Mock document data
  static final List<Map<String, dynamic>> _mockDocuments = [
    {
      'id': 'doc_1',
      'title': 'Personalausweis Max',
      'description': '<PERSON><PERSON><PERSON><PERSON> von <PERSON>',
      'type': 'idCard',
      'status': 'active',
      'documentNumber': 'T22000126',
      'issueDate': '2020-03-15T00:00:00.000Z',
      'expiryDate': '2030-03-14T00:00:00.000Z',
      'issuingAuthority': 'Stadt München',
      'imageUrls': [],
      'tags': ['Ausweis', 'Wichtig'],
      'uploadedByUserId': 'user_1',
      'familyId': 'family_1',
      'sharedWithUserIds': [],
      'isImportant': true,
      'notes': null,
      'createdAt': '2024-01-01T00:00:00.000Z',
      'updatedAt': '2024-01-01T00:00:00.000Z',
    },
    {
      'id': 'doc_2',
      'title': 'Krankenversicherung Familie',
      'description': 'Versicherungspolice der Familienversicherung',
      'type': 'insurance',
      'status': 'active',
      'documentNumber': 'KV-2024-001',
      'issueDate': '2024-01-01T00:00:00.000Z',
      'expiryDate': '2024-12-31T00:00:00.000Z',
      'issuingAuthority': 'AOK Bayern',
      'imageUrls': [],
      'tags': ['Versicherung', 'Gesundheit'],
      'uploadedByUserId': 'user_2',
      'familyId': 'family_1',
      'sharedWithUserIds': ['user_1', 'user_3'],
      'isImportant': true,
      'notes': null,
      'createdAt': '2024-01-01T00:00:00.000Z',
      'updatedAt': '2024-01-01T00:00:00.000Z',
    },
  ];

  Future<List<DocumentModel>> getDocumentsByFamily(String familyId) async {
    await Future.delayed(const Duration(milliseconds: 300));
    
    try {
      final documents = await _getDocuments();
      final familyDocuments = documents
          .where((document) => document['familyId'] == familyId)
          .map((documentMap) => DocumentModel.fromJson(documentMap))
          .toList();
      
      // Sort by creation date (newest first)
      familyDocuments.sort((a, b) => b.createdAt.compareTo(a.createdAt));
      
      return familyDocuments;
    } catch (e) {
      throw Exception('Failed to get family documents: $e');
    }
  }

  Future<List<DocumentModel>> getDocumentsByType(String familyId, DocumentType type) async {
    await Future.delayed(const Duration(milliseconds: 300));
    
    try {
      final documents = await getDocumentsByFamily(familyId);
      return documents.where((document) => document.type == type).toList();
    } catch (e) {
      throw Exception('Failed to get documents by type: $e');
    }
  }

  Future<List<DocumentModel>> getImportantDocuments(String familyId) async {
    await Future.delayed(const Duration(milliseconds: 300));
    
    try {
      final documents = await getDocumentsByFamily(familyId);
      return documents.where((document) => document.isImportant).toList();
    } catch (e) {
      throw Exception('Failed to get important documents: $e');
    }
  }

  Future<List<DocumentModel>> getExpiringDocuments(String familyId) async {
    await Future.delayed(const Duration(milliseconds: 300));
    
    try {
      final documents = await getDocumentsByFamily(familyId);
      return documents.where((document) => document.isExpiringSoon || document.isExpired).toList();
    } catch (e) {
      throw Exception('Failed to get expiring documents: $e');
    }
  }

  Future<DocumentModel?> createDocument({
    required String title,
    String? description,
    DocumentType type = DocumentType.other,
    DocumentStatus status = DocumentStatus.active,
    String? documentNumber,
    DateTime? issueDate,
    DateTime? expiryDate,
    String? issuingAuthority,
    List<String> imageUrls = const [],
    List<String> tags = const [],
    required String uploadedByUserId,
    required String familyId,
    List<String> sharedWithUserIds = const [],
    bool isImportant = false,
    String? notes,
  }) async {
    await Future.delayed(const Duration(milliseconds: 500));
    
    try {
      final documents = await _getDocuments();
      final now = DateTime.now();
      
      final newDocument = DocumentModel(
        id: _uuid.v4(),
        title: title,
        description: description,
        type: type,
        status: status,
        documentNumber: documentNumber,
        issueDate: issueDate,
        expiryDate: expiryDate,
        issuingAuthority: issuingAuthority,
        imageUrls: imageUrls,
        tags: tags,
        uploadedByUserId: uploadedByUserId,
        familyId: familyId,
        sharedWithUserIds: sharedWithUserIds,
        isImportant: isImportant,
        notes: notes,
        createdAt: now,
        updatedAt: now,
      );
      
      documents.add(newDocument.toJson());
      await _saveDocuments(documents);
      
      return newDocument;
    } catch (e) {
      throw Exception('Failed to create document: $e');
    }
  }

  Future<DocumentModel?> updateDocument(DocumentModel document) async {
    await Future.delayed(const Duration(milliseconds: 500));
    
    try {
      final documents = await _getDocuments();
      final documentIndex = documents.indexWhere((d) => d['id'] == document.id);
      
      if (documentIndex != -1) {
        final updatedDocument = document.copyWith(updatedAt: DateTime.now());
        documents[documentIndex] = updatedDocument.toJson();
        await _saveDocuments(documents);
        return updatedDocument;
      }
      return null;
    } catch (e) {
      throw Exception('Failed to update document: $e');
    }
  }

  Future<bool> toggleImportant(String documentId) async {
    await Future.delayed(const Duration(milliseconds: 300));
    
    try {
      final documents = await _getDocuments();
      final documentIndex = documents.indexWhere((document) => document['id'] == documentId);
      
      if (documentIndex != -1) {
        final documentMap = documents[documentIndex];
        documentMap['isImportant'] = !(documentMap['isImportant'] as bool? ?? false);
        documentMap['updatedAt'] = DateTime.now().toIso8601String();
        
        documents[documentIndex] = documentMap;
        await _saveDocuments(documents);
        return true;
      }
      return false;
    } catch (e) {
      throw Exception('Failed to toggle important: $e');
    }
  }

  Future<bool> deleteDocument(String documentId) async {
    await Future.delayed(const Duration(milliseconds: 500));
    
    try {
      final documents = await _getDocuments();
      final documentIndex = documents.indexWhere((document) => document['id'] == documentId);
      
      if (documentIndex != -1) {
        documents.removeAt(documentIndex);
        await _saveDocuments(documents);
        return true;
      }
      return false;
    } catch (e) {
      throw Exception('Failed to delete document: $e');
    }
  }

  Future<DocumentModel?> getDocumentById(String documentId) async {
    await Future.delayed(const Duration(milliseconds: 200));
    
    try {
      final documents = await _getDocuments();
      final documentMap = documents.firstWhere(
        (document) => document['id'] == documentId,
        orElse: () => <String, dynamic>{},
      );
      
      if (documentMap.isNotEmpty) {
        return DocumentModel.fromJson(documentMap);
      }
      return null;
    } catch (e) {
      throw Exception('Failed to get document: $e');
    }
  }

  Future<List<DocumentModel>> searchDocuments(String familyId, String query) async {
    await Future.delayed(const Duration(milliseconds: 300));
    
    try {
      final documents = await getDocumentsByFamily(familyId);
      final lowercaseQuery = query.toLowerCase();
      
      return documents.where((document) {
        return document.title.toLowerCase().contains(lowercaseQuery) ||
               (document.description?.toLowerCase().contains(lowercaseQuery) ?? false) ||
               (document.documentNumber?.toLowerCase().contains(lowercaseQuery) ?? false) ||
               document.tags.any((tag) => tag.toLowerCase().contains(lowercaseQuery));
      }).toList();
    } catch (e) {
      throw Exception('Failed to search documents: $e');
    }
  }

  Future<ScannedDocumentModel?> scanDocument(String imagePath) async {
    await Future.delayed(const Duration(seconds: 2)); // Simulate OCR processing
    
    try {
      // Mock OCR results
      final scannedDocument = ScannedDocumentModel(
        id: _uuid.v4(),
        originalImagePath: imagePath,
        processedImagePath: imagePath,
        extractedText: 'Muster-Text aus dem gescannten Dokument...',
        extractedData: {
          'documentType': 'idCard',
          'name': 'Max Mustermann',
          'documentNumber': 'T22000126',
          'expiryDate': '2030-03-14',
        },
        detectedType: DocumentType.idCard,
        confidence: 0.85,
        scannedAt: DateTime.now(),
      );
      
      return scannedDocument;
    } catch (e) {
      throw Exception('Failed to scan document: $e');
    }
  }

  Future<DocumentModel?> createDocumentFromScan(
    ScannedDocumentModel scannedDocument,
    String familyId,
    String uploadedByUserId,
  ) async {
    await Future.delayed(const Duration(milliseconds: 500));
    
    try {
      final extractedData = scannedDocument.extractedData ?? {};
      
      return await createDocument(
        title: extractedData['name'] ?? 'Gescanntes Dokument',
        description: 'Automatisch gescanntes Dokument',
        type: scannedDocument.detectedType ?? DocumentType.other,
        documentNumber: extractedData['documentNumber'],
        expiryDate: extractedData['expiryDate'] != null 
            ? DateTime.tryParse(extractedData['expiryDate']) 
            : null,
        imageUrls: [scannedDocument.processedImagePath ?? scannedDocument.originalImagePath],
        tags: ['Gescannt'],
        uploadedByUserId: uploadedByUserId,
        familyId: familyId,
        notes: 'Automatisch gescannt mit ${(scannedDocument.confidence * 100).toStringAsFixed(1)}% Genauigkeit',
      );
    } catch (e) {
      throw Exception('Failed to create document from scan: $e');
    }
  }

  Future<List<Map<String, dynamic>>> _getDocuments() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final documentsJson = prefs.getString(_documentsKey);
      
      if (documentsJson != null) {
        final documentsList = jsonDecode(documentsJson) as List;
        return documentsList.cast<Map<String, dynamic>>();
      } else {
        await _saveDocuments(_mockDocuments);
        return List<Map<String, dynamic>>.from(_mockDocuments);
      }
    } catch (e) {
      return List<Map<String, dynamic>>.from(_mockDocuments);
    }
  }

  Future<void> _saveDocuments(List<Map<String, dynamic>> documents) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final documentsJson = jsonEncode(documents);
      await prefs.setString(_documentsKey, documentsJson);
    } catch (e) {
      throw Exception('Failed to save documents: $e');
    }
  }
}
