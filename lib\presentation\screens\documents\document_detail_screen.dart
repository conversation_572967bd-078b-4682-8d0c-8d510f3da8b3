import 'package:flutter/material.dart';
import '../../widgets/common/custom_card.dart';
import '../../../core/constants/app_constants.dart';

class DocumentDetailScreen extends StatelessWidget {
  final String documentId;
  
  const DocumentDetailScreen({super.key, required this.documentId});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Dokument Details')),
      body: Center(
        child: CustomCard(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Icon(Icons.description, size: 64),
              const SizedBox(height: AppConstants.mediumSpacing),
              const Text('Dokument Details', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
              Text('Document ID: $documentId'),
              const Text('Wird bald implementiert'),
            ],
          ),
        ),
      ),
    );
  }
}
