import 'package:flutter/material.dart';
import '../../widgets/common/custom_card.dart';
import '../../../core/constants/app_constants.dart';

class ContactDetailScreen extends StatelessWidget {
  final String userId;
  
  const ContactDetailScreen({super.key, required this.userId});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Kontakt Details')),
      body: Center(
        child: CustomCard(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Icon(Icons.person, size: 64),
              const SizedBox(height: AppConstants.mediumSpacing),
              const Text('Kontakt Details', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
              Text('User ID: $userId'),
              const Text('Wird bald implementiert'),
            ],
          ),
        ),
      ),
    );
  }
}
