import 'package:equatable/equatable.dart';

enum RecipeCategory {
  breakfast,
  lunch,
  dinner,
  snack,
  dessert,
  drink,
  vegetarian,
  vegan,
  glutenFree,
  other
}

enum RecipeDifficulty { easy, medium, hard }

class RecipeModel extends Equatable {
  final String id;
  final String title;
  final String? description;
  final String? imageUrl;
  final RecipeCategory category;
  final RecipeDifficulty difficulty;
  final int preparationTimeMinutes;
  final int cookingTimeMinutes;
  final int servings;
  final List<RecipeIngredient> ingredients;
  final List<RecipeStep> steps;
  final List<String> tags;
  final double? rating;
  final int ratingCount;
  final String createdByUserId;
  final String familyId;
  final bool isFavorite;
  final String? nutritionInfo;
  final DateTime createdAt;
  final DateTime updatedAt;

  const RecipeModel({
    required this.id,
    required this.title,
    this.description,
    this.imageUrl,
    this.category = RecipeCategory.other,
    this.difficulty = RecipeDifficulty.medium,
    required this.preparationTimeMinutes,
    required this.cookingTimeMinutes,
    required this.servings,
    this.ingredients = const [],
    this.steps = const [],
    this.tags = const [],
    this.rating,
    this.ratingCount = 0,
    required this.createdByUserId,
    required this.familyId,
    this.isFavorite = false,
    this.nutritionInfo,
    required this.createdAt,
    required this.updatedAt,
  });

  int get totalTimeMinutes => preparationTimeMinutes + cookingTimeMinutes;

  String get categoryText {
    switch (category) {
      case RecipeCategory.breakfast:
        return 'Frühstück';
      case RecipeCategory.lunch:
        return 'Mittagessen';
      case RecipeCategory.dinner:
        return 'Abendessen';
      case RecipeCategory.snack:
        return 'Snack';
      case RecipeCategory.dessert:
        return 'Dessert';
      case RecipeCategory.drink:
        return 'Getränk';
      case RecipeCategory.vegetarian:
        return 'Vegetarisch';
      case RecipeCategory.vegan:
        return 'Vegan';
      case RecipeCategory.glutenFree:
        return 'Glutenfrei';
      case RecipeCategory.other:
        return 'Sonstiges';
    }
  }

  String get difficultyText {
    switch (difficulty) {
      case RecipeDifficulty.easy:
        return 'Einfach';
      case RecipeDifficulty.medium:
        return 'Mittel';
      case RecipeDifficulty.hard:
        return 'Schwer';
    }
  }

  RecipeModel copyWith({
    String? id,
    String? title,
    String? description,
    String? imageUrl,
    RecipeCategory? category,
    RecipeDifficulty? difficulty,
    int? preparationTimeMinutes,
    int? cookingTimeMinutes,
    int? servings,
    List<RecipeIngredient>? ingredients,
    List<RecipeStep>? steps,
    List<String>? tags,
    double? rating,
    int? ratingCount,
    String? createdByUserId,
    String? familyId,
    bool? isFavorite,
    String? nutritionInfo,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return RecipeModel(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      imageUrl: imageUrl ?? this.imageUrl,
      category: category ?? this.category,
      difficulty: difficulty ?? this.difficulty,
      preparationTimeMinutes: preparationTimeMinutes ?? this.preparationTimeMinutes,
      cookingTimeMinutes: cookingTimeMinutes ?? this.cookingTimeMinutes,
      servings: servings ?? this.servings,
      ingredients: ingredients ?? this.ingredients,
      steps: steps ?? this.steps,
      tags: tags ?? this.tags,
      rating: rating ?? this.rating,
      ratingCount: ratingCount ?? this.ratingCount,
      createdByUserId: createdByUserId ?? this.createdByUserId,
      familyId: familyId ?? this.familyId,
      isFavorite: isFavorite ?? this.isFavorite,
      nutritionInfo: nutritionInfo ?? this.nutritionInfo,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'imageUrl': imageUrl,
      'category': category.name,
      'difficulty': difficulty.name,
      'preparationTimeMinutes': preparationTimeMinutes,
      'cookingTimeMinutes': cookingTimeMinutes,
      'servings': servings,
      'ingredients': ingredients.map((i) => i.toJson()).toList(),
      'steps': steps.map((s) => s.toJson()).toList(),
      'tags': tags,
      'rating': rating,
      'ratingCount': ratingCount,
      'createdByUserId': createdByUserId,
      'familyId': familyId,
      'isFavorite': isFavorite,
      'nutritionInfo': nutritionInfo,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  factory RecipeModel.fromJson(Map<String, dynamic> json) {
    return RecipeModel(
      id: json['id'] as String,
      title: json['title'] as String,
      description: json['description'] as String?,
      imageUrl: json['imageUrl'] as String?,
      category: RecipeCategory.values.firstWhere(
        (e) => e.name == json['category'],
        orElse: () => RecipeCategory.other,
      ),
      difficulty: RecipeDifficulty.values.firstWhere(
        (e) => e.name == json['difficulty'],
        orElse: () => RecipeDifficulty.medium,
      ),
      preparationTimeMinutes: json['preparationTimeMinutes'] as int,
      cookingTimeMinutes: json['cookingTimeMinutes'] as int,
      servings: json['servings'] as int,
      ingredients: (json['ingredients'] as List?)
          ?.map((i) => RecipeIngredient.fromJson(i as Map<String, dynamic>))
          .toList() ?? [],
      steps: (json['steps'] as List?)
          ?.map((s) => RecipeStep.fromJson(s as Map<String, dynamic>))
          .toList() ?? [],
      tags: List<String>.from(json['tags'] as List? ?? []),
      rating: json['rating'] != null ? (json['rating'] as num).toDouble() : null,
      ratingCount: json['ratingCount'] as int? ?? 0,
      createdByUserId: json['createdByUserId'] as String,
      familyId: json['familyId'] as String,
      isFavorite: json['isFavorite'] as bool? ?? false,
      nutritionInfo: json['nutritionInfo'] as String?,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
    );
  }

  @override
  List<Object?> get props => [
        id,
        title,
        description,
        imageUrl,
        category,
        difficulty,
        preparationTimeMinutes,
        cookingTimeMinutes,
        servings,
        ingredients,
        steps,
        tags,
        rating,
        ratingCount,
        createdByUserId,
        familyId,
        isFavorite,
        nutritionInfo,
        createdAt,
        updatedAt,
      ];
}

class RecipeIngredient extends Equatable {
  final String name;
  final double amount;
  final String unit;
  final String? notes;

  const RecipeIngredient({
    required this.name,
    required this.amount,
    required this.unit,
    this.notes,
  });

  String get displayText {
    final amountText = amount == amount.toInt() 
        ? amount.toInt().toString() 
        : amount.toString();
    return '$amountText $unit $name${notes != null ? ' ($notes)' : ''}';
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'amount': amount,
      'unit': unit,
      'notes': notes,
    };
  }

  factory RecipeIngredient.fromJson(Map<String, dynamic> json) {
    return RecipeIngredient(
      name: json['name'] as String,
      amount: (json['amount'] as num).toDouble(),
      unit: json['unit'] as String,
      notes: json['notes'] as String?,
    );
  }

  @override
  List<Object?> get props => [name, amount, unit, notes];
}

class RecipeStep extends Equatable {
  final int stepNumber;
  final String instruction;
  final int? timeMinutes;
  final String? imageUrl;

  const RecipeStep({
    required this.stepNumber,
    required this.instruction,
    this.timeMinutes,
    this.imageUrl,
  });

  Map<String, dynamic> toJson() {
    return {
      'stepNumber': stepNumber,
      'instruction': instruction,
      'timeMinutes': timeMinutes,
      'imageUrl': imageUrl,
    };
  }

  factory RecipeStep.fromJson(Map<String, dynamic> json) {
    return RecipeStep(
      stepNumber: json['stepNumber'] as int,
      instruction: json['instruction'] as String,
      timeMinutes: json['timeMinutes'] as int?,
      imageUrl: json['imageUrl'] as String?,
    );
  }

  @override
  List<Object?> get props => [stepNumber, instruction, timeMinutes, imageUrl];
}
