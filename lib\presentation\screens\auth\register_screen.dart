import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/auth_provider.dart';
import '../../widgets/common/custom_button.dart';
import '../../widgets/common/custom_text_field.dart';
import '../../../core/constants/app_constants.dart';
import '../../../core/theme/app_colors.dart';
import '../../../core/routing/app_router.dart';

class RegisterScreen extends StatefulWidget {
  const RegisterScreen({super.key});

  @override
  State<RegisterScreen> createState() => _RegisterScreenState();
}

class _RegisterScreenState extends State<RegisterScreen> {
  final _formKey = GlobalKey<FormState>();
  final _firstNameController = TextEditingController();
  final _lastNameController = TextEditingController();
  final _emailController = TextEditingController();
  final _phoneController = TextEditingController();
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  DateTime? _selectedDate;
  bool _acceptTerms = false;

  @override
  void dispose() {
    _firstNameController.dispose();
    _lastNameController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }

  Future<void> _handleRegister() async {
    if (!_formKey.currentState!.validate()) return;
    
    if (_selectedDate == null) {
      _showErrorSnackBar('Bitte wähle dein Geburtsdatum aus');
      return;
    }
    
    if (!_acceptTerms) {
      _showErrorSnackBar('Bitte akzeptiere die Nutzungsbedingungen');
      return;
    }

    final authProvider = context.read<AuthProvider>();
    final success = await authProvider.register(
      email: _emailController.text.trim(),
      password: _passwordController.text,
      firstName: _firstNameController.text.trim(),
      lastName: _lastNameController.text.trim(),
      dateOfBirth: _selectedDate!,
      phoneNumber: _phoneController.text.trim().isNotEmpty 
          ? _phoneController.text.trim() 
          : null,
    );

    if (success && mounted) {
      context.goToDashboard();
    } else if (mounted) {
      _showErrorSnackBar(authProvider.errorMessage ?? 'Registrierung fehlgeschlagen');
    }
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppColors.error,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  Future<void> _selectDate() async {
    final date = await showDatePicker(
      context: context,
      initialDate: DateTime.now().subtract(const Duration(days: 365 * 25)),
      firstDate: DateTime.now().subtract(const Duration(days: 365 * 100)),
      lastDate: DateTime.now(),
      locale: const Locale('de', 'DE'),
    );
    
    if (date != null) {
      setState(() {
        _selectedDate = date;
      });
    }
  }

  String? _validateName(String? value) {
    if (value == null || value.isEmpty) {
      return 'Dieses Feld ist erforderlich';
    }
    if (value.length < 2) {
      return 'Name muss mindestens 2 Zeichen lang sein';
    }
    return null;
  }

  String? _validateEmail(String? value) {
    if (value == null || value.isEmpty) {
      return 'E-Mail ist erforderlich';
    }
    if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value)) {
      return 'Ungültige E-Mail-Adresse';
    }
    return null;
  }

  String? _validatePassword(String? value) {
    if (value == null || value.isEmpty) {
      return 'Passwort ist erforderlich';
    }
    if (value.length < 8) {
      return 'Passwort muss mindestens 8 Zeichen lang sein';
    }
    if (!RegExp(r'^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)').hasMatch(value)) {
      return 'Passwort muss Groß-, Kleinbuchstaben und Zahlen enthalten';
    }
    return null;
  }

  String? _validateConfirmPassword(String? value) {
    if (value == null || value.isEmpty) {
      return 'Passwort bestätigen ist erforderlich';
    }
    if (value != _passwordController.text) {
      return 'Passwörter stimmen nicht überein';
    }
    return null;
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Registrierung'),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.goToLogin(),
        ),
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(AppConstants.largeSpacing),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // Header
                _buildHeader(theme),
                
                const SizedBox(height: AppConstants.extraLargeSpacing),
                
                // Registration Form
                _buildRegistrationForm(theme),
                
                const SizedBox(height: AppConstants.largeSpacing),
                
                // Terms and Conditions
                _buildTermsCheckbox(theme),
                
                const SizedBox(height: AppConstants.largeSpacing),
                
                // Register Button
                Consumer<AuthProvider>(
                  builder: (context, authProvider, child) {
                    return CustomButton(
                      text: 'Registrieren',
                      onPressed: authProvider.isLoading ? null : _handleRegister,
                      isLoading: authProvider.isLoading,
                      isFullWidth: true,
                      type: ButtonType.primary,
                      size: ButtonSize.large,
                    );
                  },
                ),
                
                const SizedBox(height: AppConstants.largeSpacing),
                
                // Login Link
                _buildLoginLink(theme),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader(ThemeData theme) {
    return Column(
      children: [
        Text(
          'Konto erstellen',
          style: theme.textTheme.headlineMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
        
        const SizedBox(height: AppConstants.smallSpacing),
        
        Text(
          'Erstelle dein Family Manager Konto',
          style: theme.textTheme.bodyLarge?.copyWith(
            color: theme.colorScheme.onSurfaceVariant,
          ),
        ),
      ],
    );
  }

  Widget _buildRegistrationForm(ThemeData theme) {
    return Column(
      children: [
        // First Name and Last Name
        Row(
          children: [
            Expanded(
              child: CustomTextField(
                label: 'Vorname',
                hint: 'Max',
                controller: _firstNameController,
                isRequired: true,
                validator: _validateName,
                textInputAction: TextInputAction.next,
              ),
            ),
            const SizedBox(width: AppConstants.mediumSpacing),
            Expanded(
              child: CustomTextField(
                label: 'Nachname',
                hint: 'Mustermann',
                controller: _lastNameController,
                isRequired: true,
                validator: _validateName,
                textInputAction: TextInputAction.next,
              ),
            ),
          ],
        ),
        
        const SizedBox(height: AppConstants.mediumSpacing),
        
        // Email
        CustomTextField(
          label: 'E-Mail-Adresse',
          hint: '<EMAIL>',
          controller: _emailController,
          type: TextFieldType.email,
          isRequired: true,
          prefixIcon: Icons.email_outlined,
          validator: _validateEmail,
          textInputAction: TextInputAction.next,
        ),
        
        const SizedBox(height: AppConstants.mediumSpacing),
        
        // Phone (Optional)
        CustomTextField(
          label: 'Telefonnummer',
          hint: '+49 123 456789 (optional)',
          controller: _phoneController,
          type: TextFieldType.phone,
          prefixIcon: Icons.phone_outlined,
          textInputAction: TextInputAction.next,
        ),
        
        const SizedBox(height: AppConstants.mediumSpacing),
        
        // Date of Birth
        GestureDetector(
          onTap: _selectDate,
          child: Container(
            padding: const EdgeInsets.all(AppConstants.mediumSpacing),
            decoration: BoxDecoration(
              border: Border.all(color: theme.colorScheme.outline),
              borderRadius: BorderRadius.circular(AppConstants.mediumRadius),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.calendar_today_outlined,
                  color: theme.colorScheme.onSurfaceVariant,
                ),
                const SizedBox(width: AppConstants.mediumSpacing),
                Expanded(
                  child: Text(
                    _selectedDate != null
                        ? '${_selectedDate!.day}.${_selectedDate!.month}.${_selectedDate!.year}'
                        : 'Geburtsdatum auswählen *',
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: _selectedDate != null
                          ? theme.colorScheme.onSurface
                          : theme.colorScheme.onSurfaceVariant,
                    ),
                  ),
                ),
                Icon(
                  Icons.arrow_drop_down,
                  color: theme.colorScheme.onSurfaceVariant,
                ),
              ],
            ),
          ),
        ),
        
        const SizedBox(height: AppConstants.mediumSpacing),
        
        // Password
        CustomTextField(
          label: 'Passwort',
          hint: 'Mindestens 8 Zeichen',
          controller: _passwordController,
          type: TextFieldType.password,
          isRequired: true,
          prefixIcon: Icons.lock_outline,
          validator: _validatePassword,
          textInputAction: TextInputAction.next,
        ),
        
        const SizedBox(height: AppConstants.mediumSpacing),
        
        // Confirm Password
        CustomTextField(
          label: 'Passwort bestätigen',
          hint: 'Passwort wiederholen',
          controller: _confirmPasswordController,
          type: TextFieldType.password,
          isRequired: true,
          prefixIcon: Icons.lock_outline,
          validator: _validateConfirmPassword,
          textInputAction: TextInputAction.done,
        ),
      ],
    );
  }

  Widget _buildTermsCheckbox(ThemeData theme) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Checkbox(
          value: _acceptTerms,
          onChanged: (value) {
            setState(() {
              _acceptTerms = value ?? false;
            });
          },
        ),
        Expanded(
          child: GestureDetector(
            onTap: () {
              setState(() {
                _acceptTerms = !_acceptTerms;
              });
            },
            child: Text.rich(
              TextSpan(
                text: 'Ich akzeptiere die ',
                style: theme.textTheme.bodyMedium,
                children: [
                  TextSpan(
                    text: 'Nutzungsbedingungen',
                    style: TextStyle(
                      color: theme.colorScheme.primary,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const TextSpan(text: ' und die '),
                  TextSpan(
                    text: 'Datenschutzerklärung',
                    style: TextStyle(
                      color: theme.colorScheme.primary,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildLoginLink(ThemeData theme) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Text(
          'Bereits ein Konto? ',
          style: theme.textTheme.bodyMedium?.copyWith(
            color: theme.colorScheme.onSurfaceVariant,
          ),
        ),
        TextButton(
          onPressed: () => context.goToLogin(),
          child: Text(
            'Anmelden',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.primary,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      ],
    );
  }
}
